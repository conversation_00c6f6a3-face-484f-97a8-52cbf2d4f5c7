// lib/exceptions/two_factor_required_exception.dart

class TwoFactorRequiredException implements Exception {
  final String phoneNumber;
  final String message;
  final String title;

  TwoFactorRequiredException({
    required this.phoneNumber,
    this.message = 'Two-factor authentication is required to complete login',
    this.title = '2FA Required',
  });

  @override
  String toString() {
    return 'TwoFactorRequiredException: $message (phoneNumber: $phoneNumber)';
  }
}
