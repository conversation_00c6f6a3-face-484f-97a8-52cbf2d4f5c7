import 'package:aslaa/constant.dart';
import 'package:aslaa/flutter_flow/flutter_flow_util.dart';
import 'package:aslaa/models/device.dart';
import 'package:aslaa/models/user.dart';
import 'package:aslaa/providers/app_provider.dart';
import 'package:awesome_snackbar_content/awesome_snackbar_content.dart';
import 'package:flutter/material.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:http/http.dart' as http;

class ProfileServices {
  // Save device information
  static Future<void> saveDevice({
    required BuildContext context,
    required AppProvider authProvider,
    required String deviceName,
    required String deviceNumber,
    required String? uix,
    required bool isDefault,
    required String deviceType,
    required String deviceMode,
    Device? selectedDevice,
  }) async {
    try {
      SharedPreferences prefs = await SharedPreferences.getInstance();
      String token = prefs.getString('token') ?? authProvider.user!.token;

      Map<String, String> headers = {
        'Content-Type': 'application/json; charset=UTF-8',
        'Authorization': 'Bearer $token',
      };

      Map<String, dynamic> body = {
        "deviceName": deviceName,
        "deviceNumber": deviceNumber,
        "uix": uix,
        "isDefault": isDefault,
        "type": deviceType.toLowerCase(),
        "phoneNumber": authProvider.user?.phoneNumber,
        "_id": deviceMode == 'edit' ? selectedDevice?.id : '',
      };

      var response = await http.post(
        Uri.parse('$API_HOST/api/device/register'),
        headers: headers,
        body: jsonEncode(body),
      );

      if (response.statusCode == 200) {
        Map<String, dynamic> json = jsonDecode(response.body);
        if (json['success']) {
          _showSnackbar(context, "Saved Device Information Success", "Success",
              ContentType.success);
          authProvider.reload();
        } else {
          _showSnackbar(
              context,
              json['message'] ?? 'Please check your device details',
              "Warning",
              ContentType.warning);
        }
      } else {
        _showSnackbar(context, "Internal Server Error - ${response.statusCode}",
            "Failed", ContentType.failure);
      }
    } catch (err) {
      _showSnackbar(context, "Please check your network connection", "Failed",
          ContentType.failure);
    }
  }

  // Delete device
  static Future<void> deleteDevice({
    required BuildContext context,
    required AppProvider authProvider,
    required Device? selectedDevice,
  }) async {
    try {
      if (selectedDevice == null) return;

      SharedPreferences prefs = await SharedPreferences.getInstance();
      String token = prefs.getString('token') ?? authProvider.user!.token;

      Map<String, String> headers = {
        'Content-Type': 'application/json; charset=UTF-8',
        'Authorization': 'Bearer $token',
      };

      var response = await http.post(
        Uri.parse('$API_HOST/api/device/delete'),
        headers: headers,
        body: jsonEncode({
          'deviceNumber': selectedDevice.deviceNumber,
        }),
      );

      if (response.statusCode == 200) {
        Map<String, dynamic> json = jsonDecode(response.body);
        if (json['success']) {
          _showSnackbar(
              context, "Deleted Device", "Success", ContentType.success);
          authProvider.reload();
        } else {
          _showSnackbar(
              context,
              json['message'] ?? 'Please check your device details',
              "Warning",
              ContentType.warning);
        }
      } else {
        _showSnackbar(context, "Internal Server Error - ${response.statusCode}",
            "Failed", ContentType.failure);
      }
    } catch (err) {
      _showSnackbar(context, "Please check your network connection", "Failed",
          ContentType.failure);
    }
  }

  // Update password
  static Future<void> updatePassword({
    required BuildContext context,
    required AppProvider authProvider,
    required String oldPassword,
    required String newPassword,
    required String confirmPassword,
  }) async {
    try {
      User user = authProvider.user!;

      if (confirmPassword != newPassword) {
        _showSnackbar(
            context,
            "Please check your password and confirm password",
            "Failed",
            ContentType.failure);
        return;
      }

      if (oldPassword != user.password) {
        _showSnackbar(context, "Your previous password is not matched",
            "Failed", ContentType.failure);
        return;
      }

      SharedPreferences prefs = await SharedPreferences.getInstance();
      String token = prefs.getString('token') ?? user.token;

      Map<String, String> headers = {
        'Content-Type': 'application/json; charset=UTF-8',
        'Authorization': 'Bearer $token',
      };

      Map<String, String> body = {
        "oldPinCode": oldPassword,
        "newPinCode": newPassword,
        "phoneNumber": user.phoneNumber,
        "username": user.username,
      };

      var response = await http.post(
        Uri.parse('$API_HOST/api/auth/set-pincode'),
        headers: headers,
        body: jsonEncode(body),
      );

      if (response.statusCode == 200) {
        _showSnackbar(
            context, "Saved Security Success", "Success", ContentType.success);
        authProvider.updatePassword(password: newPassword);
      } else {
        _showSnackbar(context, "Internal Server Error - ${response.statusCode}",
            "Failed", ContentType.failure);
      }
    } catch (err) {
      _showSnackbar(context, "Please check your network connection", "Failed",
          ContentType.failure);
    }
  }

  // Helper method to show snackbar
  static void _showSnackbar(
      BuildContext context, String message, String title, ContentType type) {
    showAnimatedSnackbar(context, message, title, type);
  }
}
