// lib/utils/totp_utils.dart

import 'package:otp/otp.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:flutter/foundation.dart';

class TOTPUtils {
  static const String _secretKey = 'totp_secret';

  /// Generate TOTP code from secret
  static String generateTOTP(String secret) {
    try {
      return OTP.generateTOTPCodeString(
        secret,
        DateTime.now().millisecondsSinceEpoch,
        length: 6,
        interval: 30,
        algorithm: Algorithm.SHA1,
        isGoogle: true,
      );
    } catch (error) {
      if (kDebugMode) {
        print('Error generating TOTP: $error');
      }
      return '';
    }
  }

  /// Verify TOTP code against secret
  static bool verifyTOTP(String secret, String code) {
    try {
      final currentTime = DateTime.now().millisecondsSinceEpoch;

      // Check current time window
      final currentCode = OTP.generateTOTPCodeString(
        secret,
        currentTime,
        length: 6,
        interval: 30,
        algorithm: Algorithm.SHA1,
        isGoogle: true,
      );

      if (currentCode == code) return true;

      // Check previous time window (30 seconds ago)
      final previousCode = OTP.generateTOTPCodeString(
        secret,
        currentTime - 30000,
        length: 6,
        interval: 30,
        algorithm: Algorithm.SHA1,
        isGoogle: true,
      );

      if (previousCode == code) return true;

      // Check next time window (30 seconds ahead)
      final nextCode = OTP.generateTOTPCodeString(
        secret,
        currentTime + 30000,
        length: 6,
        interval: 30,
        algorithm: Algorithm.SHA1,
        isGoogle: true,
      );

      return nextCode == code;
    } catch (error) {
      if (kDebugMode) {
        print('Error verifying TOTP: $error');
      }
      return false;
    }
  }

  /// Get remaining seconds in current TOTP window
  static int getRemainingSeconds() {
    final now = DateTime.now().millisecondsSinceEpoch ~/ 1000;
    return 30 - (now % 30);
  }

  /// Store TOTP secret securely (for testing purposes only)
  static Future<void> storeSecret(String secret) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      await prefs.setString(_secretKey, secret);
    } catch (error) {
      if (kDebugMode) {
        print('Error storing TOTP secret: $error');
      }
    }
  }

  /// Retrieve stored TOTP secret (for testing purposes only)
  static Future<String?> getStoredSecret() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      return prefs.getString(_secretKey);
    } catch (error) {
      if (kDebugMode) {
        print('Error retrieving TOTP secret: $error');
      }
      return null;
    }
  }

  /// Clear stored TOTP secret
  static Future<void> clearStoredSecret() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      await prefs.remove(_secretKey);
    } catch (error) {
      if (kDebugMode) {
        print('Error clearing TOTP secret: $error');
      }
    }
  }

  /// Validate TOTP code format
  static bool isValidTOTPFormat(String code) {
    return RegExp(r'^\d{6}$').hasMatch(code);
  }

  /// Validate backup code format
  static bool isValidBackupCodeFormat(String code) {
    return RegExp(r'^[A-Z0-9]{8}$').hasMatch(code.toUpperCase());
  }

  /// Format backup code for display
  static String formatBackupCode(String code) {
    if (code.length == 8) {
      return '${code.substring(0, 4)}-${code.substring(4)}';
    }
    return code;
  }

  /// Parse formatted backup code
  static String parseBackupCode(String formattedCode) {
    return formattedCode.replaceAll('-', '').toUpperCase();
  }

  /// Generate QR code URL for Google Authenticator
  static String generateQRCodeUrl(
      String secret, String accountName, String issuer) {
    final encodedAccountName = Uri.encodeComponent(accountName);
    final encodedIssuer = Uri.encodeComponent(issuer);

    return 'otpauth://totp/$encodedIssuer:$encodedAccountName?secret=$secret&issuer=$encodedIssuer';
  }

  /// Extract secret from QR code URL
  static String? extractSecretFromUrl(String url) {
    try {
      final uri = Uri.parse(url);
      return uri.queryParameters['secret'];
    } catch (error) {
      if (kDebugMode) {
        print('Error extracting secret from URL: $error');
      }
      return null;
    }
  }

  /// Validate secret format
  static bool isValidSecret(String secret) {
    // Base32 alphabet
    final base32Regex = RegExp(r'^[A-Z2-7]+=*$');
    return secret.isNotEmpty && base32Regex.hasMatch(secret.toUpperCase());
  }

  /// Format secret for display (with spaces every 4 characters)
  static String formatSecretForDisplay(String secret) {
    final formatted = StringBuffer();
    for (int i = 0; i < secret.length; i += 4) {
      if (i > 0) formatted.write(' ');
      formatted.write(secret.substring(i, (i + 4).clamp(0, secret.length)));
    }
    return formatted.toString();
  }

  /// Parse formatted secret (remove spaces)
  static String parseFormattedSecret(String formattedSecret) {
    return formattedSecret.replaceAll(' ', '').toUpperCase();
  }
}
