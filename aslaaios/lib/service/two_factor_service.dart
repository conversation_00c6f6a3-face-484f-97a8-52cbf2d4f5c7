// lib/service/two_factor_service.dart

import 'dart:convert';
import 'package:http/http.dart' as http;
import 'package:flutter/foundation.dart';
import '../constant.dart';
import '../models/two_factor_auth.dart';

class TwoFactorService {
  final String _baseUrl = API_HOST;

  /// Get current 2FA status for authenticated user
  Future<TwoFactorApiResponse<TwoFactorStatus>> getTwoFactorStatus(
      String token) async {
    try {
      final response = await http.get(
        Uri.parse('$_baseUrl/api/2fa/status'),
        headers: {
          'Content-Type': 'application/json',
          'Authorization': 'Bearer $token',
        },
      );

      if (kDebugMode) {
        print('2FA Status Response: ${response.statusCode} - ${response.body}');
      }

      final jsonData = json.decode(response.body);

      if (response.statusCode == 200 && jsonData['data'] != null) {
        return TwoFactorApiResponse<TwoFactorStatus>(
          success: true,
          message: jsonData['message'] ?? 'Status retrieved successfully',
          data: TwoFactorStatus.fromJson(jsonData['data']),
          status: response.statusCode,
        );
      } else {
        return TwoFactorApiResponse<TwoFactorStatus>(
          success: false,
          message: jsonData['message'] ?? 'Failed to get 2FA status',
          status: response.statusCode,
        );
      }
    } catch (error) {
      if (kDebugMode) {
        print('Error getting 2FA status: $error');
      }
      return TwoFactorApiResponse<TwoFactorStatus>(
        success: false,
        message: 'Network error: ${error.toString()}',
        status: 500,
      );
    }
  }

  /// Setup 2FA - Generate secret and QR code
  Future<TwoFactorApiResponse<TwoFactorSetupResponse>> setup2FA(
      String token) async {
    try {
      final response = await http.post(
        Uri.parse('$_baseUrl/api/2fa/setup'),
        headers: {
          'Content-Type': 'application/json',
          'Authorization': 'Bearer $token',
        },
      );

      if (kDebugMode) {
        print('2FA Setup Response: ${response.statusCode} - ${response.body}');
      }

      final jsonData = json.decode(response.body);

      if (kDebugMode) {
        print('2FA Setup Parsed Data: $jsonData');
      }

      if (response.statusCode == 200 &&
          jsonData['status'] == 200 &&
          jsonData['data'] != null) {
        return TwoFactorApiResponse<TwoFactorSetupResponse>(
          success: true,
          message: jsonData['message'] ?? 'Setup initiated successfully',
          data: TwoFactorSetupResponse.fromJson(jsonData['data']),
          status: response.statusCode,
        );
      } else {
        return TwoFactorApiResponse<TwoFactorSetupResponse>(
          success: false,
          message: jsonData['message'] ?? 'Failed to setup 2FA',
          status: response.statusCode,
        );
      }
    } catch (error) {
      if (kDebugMode) {
        print('Error setting up 2FA: $error');
      }
      return TwoFactorApiResponse<TwoFactorSetupResponse>(
        success: false,
        message: 'Network error: ${error.toString()}',
        status: 500,
      );
    }
  }

  /// Enable 2FA after verifying TOTP token
  Future<TwoFactorApiResponse<TwoFactorEnableResponse>> enable2FA(
      String token, String totpCode) async {
    try {
      final response = await http.post(
        Uri.parse('$_baseUrl/api/2fa/enable'),
        headers: {
          'Content-Type': 'application/json',
          'Authorization': 'Bearer $token',
        },
        body: json.encode({
          'token': totpCode,
        }),
      );

      if (kDebugMode) {
        print('2FA Enable Response: ${response.statusCode} - ${response.body}');
      }

      final jsonData = json.decode(response.body);

      if (kDebugMode) {
        print('2FA Enable Parsed Data: $jsonData');
      }

      if (response.statusCode == 200 && jsonData['status'] == 200) {
        return TwoFactorApiResponse<TwoFactorEnableResponse>(
          success: true,
          message: jsonData['message'] ?? '2FA enabled successfully',
          data: TwoFactorEnableResponse.fromJson(jsonData['data']),
          status: response.statusCode,
        );
      } else {
        return TwoFactorApiResponse<TwoFactorEnableResponse>(
          success: false,
          message: jsonData['message'] ?? 'Failed to enable 2FA',
          status: response.statusCode,
        );
      }
    } catch (error) {
      if (kDebugMode) {
        print('Error enabling 2FA: $error');
      }
      return TwoFactorApiResponse<TwoFactorEnableResponse>(
        success: false,
        message: 'Network error: ${error.toString()}',
        status: 500,
      );
    }
  }

  /// Verify 2FA token during login
  Future<TwoFactorApiResponse<Map<String, dynamic>>> verify2FA(
      TwoFactorVerificationRequest request) async {
    try {
      final response = await http.post(
        Uri.parse('$_baseUrl/api/2fa/verify'),
        headers: {
          'Content-Type': 'application/json',
        },
        body: json.encode(request.toJson()),
      );

      if (kDebugMode) {
        print('2FA Verify Response: ${response.statusCode} - ${response.body}');
      }

      final jsonData = json.decode(response.body);

      if (response.statusCode == 200 && jsonData['status'] == 200) {
        return TwoFactorApiResponse<Map<String, dynamic>>(
          success: true,
          message: jsonData['message'] ?? '2FA verification successful',
          data: jsonData['data'] ?? {},
          status: response.statusCode,
        );
      } else {
        return TwoFactorApiResponse<Map<String, dynamic>>(
          success: false,
          message: jsonData['message'] ?? 'Invalid verification code',
          status: response.statusCode,
        );
      }
    } catch (error) {
      if (kDebugMode) {
        print('Error verifying 2FA: $error');
      }
      return TwoFactorApiResponse<Map<String, dynamic>>(
        success: false,
        message: 'Network error: ${error.toString()}',
        status: 500,
      );
    }
  }

  /// Disable 2FA
  Future<TwoFactorApiResponse<Map<String, dynamic>>> disable2FA(
      String token, String totpCode) async {
    try {
      final response = await http.post(
        Uri.parse('$_baseUrl/api/2fa/disable'),
        headers: {
          'Content-Type': 'application/json',
          'Authorization': 'Bearer $token',
        },
        body: json.encode({
          'token': totpCode,
        }),
      );

      if (kDebugMode) {
        print(
            '2FA Disable Response: ${response.statusCode} - ${response.body}');
      }

      final jsonData = json.decode(response.body);

      if (response.statusCode == 200 && jsonData['success'] == true) {
        return TwoFactorApiResponse<Map<String, dynamic>>(
          success: true,
          message: jsonData['message'] ?? '2FA disabled successfully',
          data: jsonData['data'] ?? {},
          status: response.statusCode,
        );
      } else {
        return TwoFactorApiResponse<Map<String, dynamic>>(
          success: false,
          message: jsonData['message'] ?? 'Failed to disable 2FA',
          status: response.statusCode,
        );
      }
    } catch (error) {
      if (kDebugMode) {
        print('Error disabling 2FA: $error');
      }
      return TwoFactorApiResponse<Map<String, dynamic>>(
        success: false,
        message: 'Network error: ${error.toString()}',
        status: 500,
      );
    }
  }

  /// Generate new backup codes
  Future<TwoFactorApiResponse<TwoFactorEnableResponse>> generateNewBackupCodes(
      String token, String totpCode) async {
    try {
      final response = await http.post(
        Uri.parse('$_baseUrl/api/2fa/backup-codes'),
        headers: {
          'Content-Type': 'application/json',
          'Authorization': 'Bearer $token',
        },
        body: json.encode({
          'token': totpCode,
        }),
      );

      if (kDebugMode) {
        print(
            'Backup Codes Response: ${response.statusCode} - ${response.body}');
      }

      final jsonData = json.decode(response.body);

      if (response.statusCode == 200 && jsonData['success'] == true) {
        return TwoFactorApiResponse<TwoFactorEnableResponse>(
          success: true,
          message: jsonData['message'] ?? 'New backup codes generated',
          data: TwoFactorEnableResponse.fromJson(jsonData['data']),
          status: response.statusCode,
        );
      } else {
        return TwoFactorApiResponse<TwoFactorEnableResponse>(
          success: false,
          message: jsonData['message'] ?? 'Failed to generate backup codes',
          status: response.statusCode,
        );
      }
    } catch (error) {
      if (kDebugMode) {
        print('Error generating backup codes: $error');
      }
      return TwoFactorApiResponse<TwoFactorEnableResponse>(
        success: false,
        message: 'Network error: ${error.toString()}',
        status: 500,
      );
    }
  }

  /// Complete login after 2FA verification (similar to web app)
  Future<TwoFactorApiResponse<Map<String, dynamic>>> completeLoginAfter2FA(
      String phoneNumber) async {
    try {
      final response = await http.post(
        Uri.parse('$_baseUrl/api/auth/complete-login-2fa'),
        headers: {
          'Content-Type': 'application/json',
        },
        body: json.encode({
          'phoneNumber': phoneNumber,
        }),
      );

      if (kDebugMode) {
        print(
            'Complete Login Response: ${response.statusCode} - ${response.body}');
      }

      final jsonData = json.decode(response.body);

      if (kDebugMode) {
        print('Complete Login Parsed Data: $jsonData');
      }

      if (response.statusCode == 200 && jsonData['success'] == true) {
        return TwoFactorApiResponse<Map<String, dynamic>>(
          success: true,
          message: jsonData['message'] ?? 'Login completed successfully',
          data: jsonData,
          status: response.statusCode,
        );
      } else {
        return TwoFactorApiResponse<Map<String, dynamic>>(
          success: false,
          message: jsonData['message'] ?? 'Failed to complete login',
          status: response.statusCode,
        );
      }
    } catch (error) {
      if (kDebugMode) {
        print('Error completing login: $error');
      }
      return TwoFactorApiResponse<Map<String, dynamic>>(
        success: false,
        message: 'Network error: ${error.toString()}',
        status: 500,
      );
    }
  }
}
