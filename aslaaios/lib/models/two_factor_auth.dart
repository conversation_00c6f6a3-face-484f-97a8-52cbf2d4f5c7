// lib/models/two_factor_auth.dart

/// Model for 2FA status response
class TwoFactorStatus {
  final bool twoFactorEnabled;
  final DateTime? twoFactorEnabledAt;
  final int unusedBackupCodes;
  final bool hasSecret;

  TwoFactorStatus({
    required this.twoFactorEnabled,
    this.twoFactorEnabledAt,
    required this.unusedBackupCodes,
    required this.hasSecret,
  });

  factory TwoFactorStatus.fromJson(Map<String, dynamic> json) {
    return TwoFactorStatus(
      twoFactorEnabled: json['twoFactorEnabled'] ?? false,
      twoFactorEnabledAt: json['twoFactorEnabledAt'] != null
          ? DateTime.parse(json['twoFactorEnabledAt'])
          : null,
      unusedBackupCodes: json['unusedBackupCodes'] ?? 0,
      hasSecret: json['hasSecret'] ?? false,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'twoFactorEnabled': twoFactorEnabled,
      'twoFactorEnabledAt': twoFactorEnabledAt?.toIso8601String(),
      'unusedBackupCodes': unusedBackupCodes,
      'hasSecret': hasSecret,
    };
  }

  TwoFactorStatus copyWith({
    bool? twoFactorEnabled,
    DateTime? twoFactorEnabledAt,
    int? unusedBackupCodes,
    bool? hasSecret,
  }) {
    return TwoFactorStatus(
      twoFactorEnabled: twoFactorEnabled ?? this.twoFactorEnabled,
      twoFactorEnabledAt: twoFactorEnabledAt ?? this.twoFactorEnabledAt,
      unusedBackupCodes: unusedBackupCodes ?? this.unusedBackupCodes,
      hasSecret: hasSecret ?? this.hasSecret,
    );
  }
}

/// Model for 2FA setup response
class TwoFactorSetupResponse {
  final String secret;
  final String qrCode;
  final String manualEntryKey;

  TwoFactorSetupResponse({
    required this.secret,
    required this.qrCode,
    required this.manualEntryKey,
  });

  factory TwoFactorSetupResponse.fromJson(Map<String, dynamic> json) {
    return TwoFactorSetupResponse(
      secret: json['secret'] ?? '',
      qrCode: json['qrCode'] ?? '',
      manualEntryKey: json['manualEntryKey'] ?? '',
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'secret': secret,
      'qrCode': qrCode,
      'manualEntryKey': manualEntryKey,
    };
  }
}

/// Model for 2FA enable response
class TwoFactorEnableResponse {
  final List<String> backupCodes;
  final String message;

  TwoFactorEnableResponse({
    required this.backupCodes,
    required this.message,
  });

  factory TwoFactorEnableResponse.fromJson(Map<String, dynamic> json) {
    return TwoFactorEnableResponse(
      backupCodes: List<String>.from(json['backupCodes'] ?? []),
      message: json['message'] ?? '',
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'backupCodes': backupCodes,
      'message': message,
    };
  }
}

/// Model for backup code
class BackupCode {
  final String code;
  final bool used;
  final DateTime? usedAt;

  BackupCode({
    required this.code,
    required this.used,
    this.usedAt,
  });

  factory BackupCode.fromJson(Map<String, dynamic> json) {
    return BackupCode(
      code: json['code'] ?? '',
      used: json['used'] ?? false,
      usedAt: json['usedAt'] != null ? DateTime.parse(json['usedAt']) : null,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'code': code,
      'used': used,
      'usedAt': usedAt?.toIso8601String(),
    };
  }
}

/// Model for 2FA verification request
class TwoFactorVerificationRequest {
  final String phoneNumber;
  final String token;
  final bool isBackupCode;

  TwoFactorVerificationRequest({
    required this.phoneNumber,
    required this.token,
    this.isBackupCode = false,
  });

  Map<String, dynamic> toJson() {
    return {
      'phoneNumber': phoneNumber,
      'token': token,
      'isBackupCode': isBackupCode,
    };
  }
}

/// Model for API response wrapper
class TwoFactorApiResponse<T> {
  final bool success;
  final String message;
  final T? data;
  final int status;

  TwoFactorApiResponse({
    required this.success,
    required this.message,
    this.data,
    required this.status,
  });

  factory TwoFactorApiResponse.fromJson(
    Map<String, dynamic> json,
    T Function(Map<String, dynamic>)? fromJsonT,
  ) {
    return TwoFactorApiResponse<T>(
      success: json['success'] ?? false,
      message: json['message'] ?? '',
      data: json['data'] != null && fromJsonT != null
          ? fromJsonT(json['data'])
          : null,
      status: json['status'] ?? 200,
    );
  }
}

/// Enum for 2FA setup steps
enum TwoFactorSetupStep {
  setup,
  verify,
  backupCodes,
}

/// Model for 2FA setup state
class TwoFactorSetupState {
  final TwoFactorSetupStep currentStep;
  final TwoFactorSetupResponse? setupResponse;
  final List<String>? backupCodes;
  final bool isLoading;
  final String? error;

  TwoFactorSetupState({
    required this.currentStep,
    this.setupResponse,
    this.backupCodes,
    this.isLoading = false,
    this.error,
  });

  TwoFactorSetupState copyWith({
    TwoFactorSetupStep? currentStep,
    TwoFactorSetupResponse? setupResponse,
    List<String>? backupCodes,
    bool? isLoading,
    String? error,
  }) {
    return TwoFactorSetupState(
      currentStep: currentStep ?? this.currentStep,
      setupResponse: setupResponse ?? this.setupResponse,
      backupCodes: backupCodes ?? this.backupCodes,
      isLoading: isLoading ?? this.isLoading,
      error: error,
    );
  }
}
