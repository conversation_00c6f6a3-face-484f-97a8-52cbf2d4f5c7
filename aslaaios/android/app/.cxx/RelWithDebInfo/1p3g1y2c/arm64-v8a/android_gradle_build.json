{"buildFiles": ["/Users/<USER>/somecode/flutter/packages/flutter_tools/gradle/src/main/groovy/CMakeLists.txt"], "cleanCommandsComponents": [["/Users/<USER>/Library/Android/sdk/cmake/3.22.1/bin/ninja", "-C", "/Users/<USER>/somecode/aslaa/app/aslaaios/android/app/.cxx/RelWithDebInfo/1p3g1y2c/arm64-v8a", "clean"]], "buildTargetsCommandComponents": ["/Users/<USER>/Library/Android/sdk/cmake/3.22.1/bin/ninja", "-C", "/Users/<USER>/somecode/aslaa/app/aslaaios/android/app/.cxx/RelWithDebInfo/1p3g1y2c/arm64-v8a", "{LIST_OF_TARGETS_TO_BUILD}"], "libraries": {}, "toolchains": {"toolchain": {"cCompilerExecutable": "/Users/<USER>/Library/Android/sdk/ndk/25.1.8937393/toolchains/llvm/prebuilt/darwin-x86_64/bin/clang.lld", "cppCompilerExecutable": "/Users/<USER>/Library/Android/sdk/ndk/25.1.8937393/toolchains/llvm/prebuilt/darwin-x86_64/bin/clang++.lld"}}, "cFileExtensions": [], "cppFileExtensions": []}