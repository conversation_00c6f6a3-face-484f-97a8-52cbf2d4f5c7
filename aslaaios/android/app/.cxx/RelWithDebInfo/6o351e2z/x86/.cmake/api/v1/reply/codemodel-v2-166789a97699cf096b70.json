{"configurations": [{"directories": [{"build": ".", "jsonFile": "directory-.-RelWithDebInfo-f5ebdc15457944623624.json", "minimumCMakeVersion": {"string": "3.6.0"}, "projectIndex": 0, "source": "."}], "name": "RelWithDebInfo", "projects": [{"directoryIndexes": [0], "name": "Project"}], "targets": []}], "kind": "codemodel", "paths": {"build": "/Users/<USER>/somecode/aslaa/web/aslaaios/android/app/.cxx/RelWithDebInfo/6o351e2z/x86", "source": "/Users/<USER>/somecode/flutter/packages/flutter_tools/gradle/src/main/groovy"}, "version": {"major": 2, "minor": 3}}