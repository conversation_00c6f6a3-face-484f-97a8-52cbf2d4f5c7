name: aslaa
description: A new Flutter project.

publish_to: 'none'

version: 2.3.0+3
environment:
  sdk: ">=3.0.0 <4.0.0"
dependencies:
  flutter:
    sdk: flutter
  flutter_localizations:
    sdk: flutter
  auto_size_text: 3.0.0
  cached_network_image: 3.2.1
  emoji_flag_converter: 1.1.0
  flutter_animate: 1.0.0
  font_awesome_flutter: 10.1.0
  from_css_color: 2.0.0
  go_router: ^12.1.1
  google_fonts: ^4.0.1
  intl: ^0.19.0
  json_path: 0.4.1
  page_transition: 2.0.4
  provider: ^6.1.1
  shared_preferences: 2.3.2
  sqflite: 2.4.0
  stop_watch_timer: 3.2.1
  timeago: 3.7.0
  url_launcher: ^6.1.8
  flutter_local_notifications: ^17.2.4
  cupertino_icons: ^1.0.0
  animated_rotation: ^2.0.0
  http: ^1.1.0
  awesome_snackbar_content: ^0.1.0
  image_picker: ^0.8.6+1
  socket_io_client: ^3.0.0
  google_maps_flutter: ^2.12.3
  custom_marker: ^1.0.0
  geolocator: ^9.0.2
  mobile_scanner: ^6.0.2
  data_table_2: ^2.3.12
  pin_code_fields: ^8.0.1
  custom_date_range_picker: ^1.0.6
  badges: ^3.0.2
  firebase_messaging: ^15.1.3
  firebase_core: ^3.6.0
  flutter_reactive_ble: ^5.0.3
  permission_handler: ^12.0.1
  mqtt_client: ^10.5.1
  flutter_animarker: ^3.2.0
  # google_maps_cluster_manager: ^3.1.0  # Commented out due to conflict with new google_maps_flutter
  event_bus: ^2.0.0
  osrm: ^0.0.5
  flutter_blue_plus: ^1.4.0
  collection: ^1.17.0
  flutter_page_lifecycle: ^1.1.0
  circular_countdown_timer: ^0.2.3
  audioplayers: ^6.1.0
  flutter_sound: ^9.28.0
  path_provider: ^2.0.1
  http_parser: ^4.0.0
  googleapis: ^13.1.0
  googleapis_auth: ^1.6.0
  flutter_launcher_icons: ^0.10.0
  connectivity_plus: ^6.1.0
  google_maps_flutter_platform_interface: ^2.12.1

  # 2FA Dependencies
  qr_flutter: ^4.1.0          # QR code generation for setup
  otp: ^3.1.4                 # TOTP generation and verification
  # flutter_secure_storage: ^6.1.0  # Secure storage for sensitive data (replaced with shared_preferences)

dev_dependencies:
  flutter_test:
    sdk: flutter

flutter_icons:
  android: true
  ios: true
  image_path: "assets/icon/app_icon.jpg"

flutter:
  uses-material-design: true

  assets:
    - assets/fonts/
    - assets/images/
    - assets/videos/
    - assets/audios/
    - assets/lottie_animations/
    - assets/rive_animations/
    - assets/pdfs/
    - assets/audios/engine.mp3
    - assets/audios/lock.mp3
    - assets/audios/beep.mp3
    - assets/icon/
    - assets/apikey/aslaaios-3bfbd2c34d88.json

  fonts:
    - family: PorscheNumber
      fonts:
        - asset: assets/fonts/digital-7.ttf
