import { lazy } from 'react';
import { Navigate } from 'react-router-dom';
// guards
import AuthGuard from '../guards/AuthGuard';
import InstallerGuard from '../guards/InstallerGuard';

// ----------------------------------------------------------------------

const InstallerDashboard = lazy(() => import('../pages/installer/InstallerDashboard'));

// ----------------------------------------------------------------------

const installerRoutes = {
  path: 'installer',
  children: [
    { path: '', element: <Navigate to="/installer/dashboard" replace /> },
    { 
      path: 'dashboard', 
      element: (
        <AuthGuard>
          <InstallerGuard>
            <InstallerDashboard />
          </InstallerGuard>
        </AuthGuard>
      ) 
    },
  ],
};

export default installerRoutes;
