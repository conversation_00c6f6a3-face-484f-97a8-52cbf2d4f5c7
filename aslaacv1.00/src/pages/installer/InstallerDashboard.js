import { useState, useCallback } from "react";
import {
  Container,
  Typo<PERSON>,
  <PERSON>,
  Card,
  CardContent,
  Button,
  TextField,
  Grid,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Snackbar,
  Alert,
  CircularProgress,
  useMediaQuery,
  useTheme,
} from "@mui/material";
import { styled } from "@mui/system";
import axios from "../../utils/axios";
import logger from "../../utils/logger";
import AddIcon from "@mui/icons-material/Add";
import SearchIcon from "@mui/icons-material/Search";
import LockResetIcon from "@mui/icons-material/LockReset";
import DevicesIcon from "@mui/icons-material/Devices";
import PersonAddIcon from "@mui/icons-material/PersonAdd";
import Page from "../../components/Page";
import Layout from "../../layout";

// Mobile-first responsive container
const StyledContainer = styled(Container)(({ theme }) => ({
  paddingTop: theme.spacing(8),
  paddingBottom: theme.spacing(4),
  display: "flex",
  flexDirection: "column",
  gap: theme.spacing(2),
  [theme.breakpoints.up('md')]: {
    paddingTop: theme.spacing(12),
  },
}));

// Mobile-friendly action button
const MobileActionButton = styled(Button)(({ theme }) => ({
  [theme.breakpoints.down('sm')]: {
    width: '100%',
    marginTop: theme.spacing(1),
  },
}));

export default function InstallerDashboard() {
  const theme = useTheme();
  const isMobile = useMediaQuery(theme.breakpoints.down('md'));

  // State management
  const [searchQuery, setSearchQuery] = useState("");
  const [searchResponse, setSearchResponse] = useState(null);
  const [isSearching, setIsSearching] = useState(false);
  
  // Dialog states
  const [openAddUserDialog, setOpenAddUserDialog] = useState(false);
  const [openAddDeviceDialog, setOpenAddDeviceDialog] = useState(false);
  const [openResetPinDialog, setOpenResetPinDialog] = useState(false);
  
  // Form states
  const [newUserPhone, setNewUserPhone] = useState("");
  const [newDeviceNumber, setNewDeviceNumber] = useState("");
  const [resetPinPhone, setResetPinPhone] = useState("");
  const [selectedUser, setSelectedUser] = useState(null);
  
  // Loading states
  const [isAddingUser, setIsAddingUser] = useState(false);
  const [isAddingDevice, setIsAddingDevice] = useState(false);
  const [isResettingPin, setIsResettingPin] = useState(false);

  // Notification state
  const [notification, setNotification] = useState({
    open: false,
    message: "",
    severity: "success",
  });

  const showNotification = useCallback((message, severity = "success") => {
    setNotification({ open: true, message, severity });
  }, []);

  const hideNotification = useCallback(() => {
    setNotification(prev => ({ ...prev, open: false }));
  }, []);

  // Search functionality
  const handleSearch = useCallback(async () => {
    const trimmedQuery = searchQuery.trim();
    if (!trimmedQuery) {
      showNotification("Please enter a search term", "warning");
      return;
    }

    setIsSearching(true);
    try {
      const paramObj = {};
      if (trimmedQuery.length >= 14) {
        paramObj.deviceNumber = trimmedQuery;
      } else {
        paramObj.phoneNumber = trimmedQuery;
      }

      const response = await axios.get("/api/admin/user/list", { params: paramObj });

      if (response.data.success) {
        const uniqueUsers = response.data.users.reduce((acc, user) => {
          if (!acc.some((u) => u.phoneNumber === user.phoneNumber)) {
            acc.push(user);
          }
          return acc;
        }, []);

        setSearchResponse({ ...response.data, users: uniqueUsers });

        if (uniqueUsers.length === 0) {
          showNotification("No users found matching your search", "info");
        }
      } else {
        showNotification("Search failed. Please try again.", "error");
      }
    } catch (error) {
      logger.error("Error searching users:", error);
      showNotification("Search failed. Please check your connection and try again.", "error");
    } finally {
      setIsSearching(false);
    }
  }, [searchQuery, showNotification]);

  // Add user functionality
  const handleAddUser = useCallback(async () => {
    if (!newUserPhone.trim()) return;

    setIsAddingUser(true);
    try {
      const response = await axios.post("/api/auth/admin-register", { 
        phoneNumber: newUserPhone.trim() 
      });
      
      if (response.data.success) {
        showNotification("User added successfully", "success");
        setNewUserPhone("");
        setOpenAddUserDialog(false);
      } else {
        showNotification(response.data.message || "Failed to add user", "error");
      }
    } catch (error) {
      logger.error("Error adding user:", error);
      showNotification("Failed to add user. Please try again.", "error");
    } finally {
      setIsAddingUser(false);
    }
  }, [newUserPhone, showNotification]);

  // Add device functionality
  const handleAddDevice = useCallback(async () => {
    if (!selectedUser || !newDeviceNumber.trim()) return;

    setIsAddingDevice(true);
    try {
      const response = await axios.post("/api/device/admin-create", {
        deviceNumber: newDeviceNumber.trim(),
        phoneNumber: selectedUser.phoneNumber,
        uix: "CarV1.2",
        type: "4g",
        isDefault: true,
        deviceName: `Device for ${selectedUser.phoneNumber}`
      });
      
      if (response.data.success) {
        showNotification("Device created successfully", "success");
        setNewDeviceNumber("");
        setOpenAddDeviceDialog(false);
        // Refresh search if there's a query
        if (searchQuery) {
          handleSearch();
        }
      } else {
        showNotification(response.data.message || "Failed to create device", "error");
      }
    } catch (error) {
      logger.error("Error creating device:", error);
      showNotification("Failed to create device. Please try again.", "error");
    } finally {
      setIsAddingDevice(false);
    }
  }, [selectedUser, newDeviceNumber, showNotification, searchQuery, handleSearch]);

  // Reset PIN functionality
  const handleResetPin = useCallback(async () => {
    if (!resetPinPhone.trim()) return;

    setIsResettingPin(true);
    try {
      const response = await axios.post("/api/admin/user/reset-pin", {
        phoneNumber: resetPinPhone.trim()
      });
      
      if (response.data.success) {
        showNotification("PIN reset to 0000 successfully", "success");
        setResetPinPhone("");
        setOpenResetPinDialog(false);
      } else {
        showNotification(response.data.message || "Failed to reset PIN", "error");
      }
    } catch (error) {
      logger.error("Error resetting PIN:", error);
      showNotification("Failed to reset PIN. Please try again.", "error");
    } finally {
      setIsResettingPin(false);
    }
  }, [resetPinPhone, showNotification]);

  return (
    <Page title="Installer Dashboard">
      <StyledContainer>
        <Layout />
        
        <Typography variant="h4" component="h1" gutterBottom>
          Installer Dashboard
        </Typography>

        {/* Quick Actions */}
        <Card>
          <CardContent>
            <Typography variant="h6" gutterBottom>
              Quick Actions
            </Typography>
            <Grid container spacing={2}>
              <Grid item xs={12} sm={6} md={3}>
                <Button
                  fullWidth
                  variant="outlined"
                  startIcon={<PersonAddIcon />}
                  onClick={() => setOpenAddUserDialog(true)}
                >
                  Add User
                </Button>
              </Grid>
              <Grid item xs={12} sm={6} md={3}>
                <Button
                  fullWidth
                  variant="outlined"
                  startIcon={<LockResetIcon />}
                  onClick={() => setOpenResetPinDialog(true)}
                >
                  Reset PIN
                </Button>
              </Grid>
            </Grid>
          </CardContent>
        </Card>

        {/* Search Section */}
        <Card>
          <CardContent>
            <Typography variant="h6" gutterBottom>
              Search Users
            </Typography>
            <Grid container spacing={2} alignItems="flex-end">
              <Grid item xs={12} sm="auto" sx={{ flexGrow: 1 }}>
                <TextField
                  fullWidth
                  variant="outlined"
                  label="Search by phone or device number"
                  value={searchQuery}
                  onChange={(e) => setSearchQuery(e.target.value)}
                  onKeyDown={(e) => {
                    if (e.key === 'Enter') {
                      handleSearch();
                    }
                  }}
                  disabled={isSearching}
                />
              </Grid>
              <Grid item>
                <MobileActionButton
                  variant="contained"
                  color="primary"
                  onClick={handleSearch}
                  startIcon={isSearching ? <CircularProgress size={16} /> : <SearchIcon />}
                  disabled={isSearching || !searchQuery.trim()}
                >
                  {isSearching ? 'Searching...' : 'Search'}
                </MobileActionButton>
              </Grid>
            </Grid>
          </CardContent>
        </Card>

        {/* Search Results */}
        {searchResponse && searchResponse.users?.length > 0 && (
          <Card>
            <CardContent>
              <Typography variant="h6" gutterBottom>
                Search Results ({searchResponse.users.length})
              </Typography>
              {searchResponse.users.map((user) => (
                <Card key={user._id} variant="outlined" sx={{ mb: 2 }}>
                  <CardContent>
                    <Grid container spacing={2} alignItems="center">
                      <Grid item xs={12} sm={3}>
                        <Typography variant="subtitle1">
                          {user.phoneNumber}
                        </Typography>
                      </Grid>
                      <Grid item xs={12} sm={3}>
                        <Typography variant="body2" color="text.secondary">
                          Device: {user.devices?.deviceNumber || 'No device assigned'}
                        </Typography>
                      </Grid>
                      <Grid item xs={12} sm={6}>
                        <Box sx={{ display: 'flex', gap: 1, flexWrap: 'wrap' }}>
                          {!user.devices?.deviceNumber && (
                            <Button
                              size="small"
                              variant="outlined"
                              color="success"
                              startIcon={<DevicesIcon />}
                              onClick={() => {
                                setSelectedUser(user);
                                setOpenAddDeviceDialog(true);
                              }}
                            >
                              Add Device
                            </Button>
                          )}
                          <Button
                            size="small"
                            variant="outlined"
                            color="warning"
                            startIcon={<LockResetIcon />}
                            onClick={() => {
                              setResetPinPhone(user.phoneNumber);
                              setOpenResetPinDialog(true);
                            }}
                          >
                            Reset PIN
                          </Button>
                        </Box>
                      </Grid>
                    </Grid>
                  </CardContent>
                </Card>
              ))}
            </CardContent>
          </Card>
        )}

        {/* Add User Dialog */}
        <Dialog
          open={openAddUserDialog}
          onClose={() => {
            if (!isAddingUser) {
              setOpenAddUserDialog(false);
              setNewUserPhone("");
            }
          }}
          maxWidth="sm"
          fullWidth
        >
          <DialogTitle>Add New User</DialogTitle>
          <DialogContent>
            <TextField
              autoFocus
              margin="dense"
              label="Phone Number"
              type="text"
              fullWidth
              variant="outlined"
              value={newUserPhone}
              onChange={(e) => setNewUserPhone(e.target.value)}
              required
              disabled={isAddingUser}
              placeholder="Enter phone number"
            />
          </DialogContent>
          <DialogActions>
            <Button 
              onClick={() => {
                setOpenAddUserDialog(false);
                setNewUserPhone("");
              }} 
              disabled={isAddingUser}
            >
              Cancel
            </Button>
            <Button
              onClick={handleAddUser}
              color="primary"
              variant="contained"
              disabled={isAddingUser || !newUserPhone.trim()}
              startIcon={isAddingUser ? <CircularProgress size={16} /> : null}
            >
              {isAddingUser ? 'Adding...' : 'Add User'}
            </Button>
          </DialogActions>
        </Dialog>

        {/* Add Device Dialog */}
        <Dialog
          open={openAddDeviceDialog}
          onClose={() => {
            if (!isAddingDevice) {
              setOpenAddDeviceDialog(false);
              setNewDeviceNumber("");
            }
          }}
          maxWidth="sm"
          fullWidth
        >
          <DialogTitle>
            Add Device for {selectedUser?.phoneNumber}
          </DialogTitle>
          <DialogContent>
            <Typography variant="body2" color="text.secondary" sx={{ mb: 2 }}>
              Device will be created with default values: Type: 4g, UIX: CarV1.2, Default: Yes
            </Typography>
            <TextField
              autoFocus
              margin="dense"
              label="Device Number"
              type="text"
              fullWidth
              variant="outlined"
              value={newDeviceNumber}
              onChange={(e) => setNewDeviceNumber(e.target.value)}
              required
              disabled={isAddingDevice}
              placeholder="Enter device number"
            />
          </DialogContent>
          <DialogActions>
            <Button 
              onClick={() => {
                setOpenAddDeviceDialog(false);
                setNewDeviceNumber("");
              }} 
              disabled={isAddingDevice}
            >
              Cancel
            </Button>
            <Button
              onClick={handleAddDevice}
              color="success"
              variant="contained"
              disabled={isAddingDevice || !newDeviceNumber.trim()}
              startIcon={isAddingDevice ? <CircularProgress size={16} /> : null}
            >
              {isAddingDevice ? 'Creating...' : 'Create Device'}
            </Button>
          </DialogActions>
        </Dialog>

        {/* Reset PIN Dialog */}
        <Dialog
          open={openResetPinDialog}
          onClose={() => {
            if (!isResettingPin) {
              setOpenResetPinDialog(false);
              setResetPinPhone("");
            }
          }}
          maxWidth="sm"
          fullWidth
        >
          <DialogTitle>Reset User PIN</DialogTitle>
          <DialogContent>
            <Typography variant="body2" color="text.secondary" sx={{ mb: 2 }}>
              This will reset the user's PIN to "0000"
            </Typography>
            <TextField
              autoFocus
              margin="dense"
              label="Phone Number"
              type="text"
              fullWidth
              variant="outlined"
              value={resetPinPhone}
              onChange={(e) => setResetPinPhone(e.target.value)}
              required
              disabled={isResettingPin}
              placeholder="Enter phone number"
            />
          </DialogContent>
          <DialogActions>
            <Button 
              onClick={() => {
                setOpenResetPinDialog(false);
                setResetPinPhone("");
              }} 
              disabled={isResettingPin}
            >
              Cancel
            </Button>
            <Button
              onClick={handleResetPin}
              color="warning"
              variant="contained"
              disabled={isResettingPin || !resetPinPhone.trim()}
              startIcon={isResettingPin ? <CircularProgress size={16} /> : null}
            >
              {isResettingPin ? 'Resetting...' : 'Reset PIN'}
            </Button>
          </DialogActions>
        </Dialog>

        {/* Notification Snackbar */}
        <Snackbar
          open={notification.open}
          autoHideDuration={4000}
          onClose={hideNotification}
          anchorOrigin={{
            vertical: isMobile ? 'bottom' : 'top',
            horizontal: 'center'
          }}
        >
          <Alert
            onClose={hideNotification}
            severity={notification.severity}
            variant="filled"
            sx={{ width: '100%' }}
          >
            {notification.message}
          </Alert>
        </Snackbar>
      </StyledContainer>
    </Page>
  );
}
