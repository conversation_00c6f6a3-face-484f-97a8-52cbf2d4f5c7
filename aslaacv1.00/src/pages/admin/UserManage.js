import { useState, useEffect, useMemo, useCallback } from "react";
import {
  Container,
  Typography,
  Box,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Paper,
  IconButton,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogContentText,
  DialogActions,
  Button,
  TextField,
  Grid,
  Card,
  CardContent,
  Snackbar,
  Alert,
  Chip,
  CircularProgress,
  useMediaQuery,
  useTheme,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
} from "@mui/material";
import { styled } from "@mui/system";
import axios from "../../utils/axios";
import logger from "../../utils/logger";
import AddIcon from "@mui/icons-material/Add";
import SearchIcon from "@mui/icons-material/Search";
import VisibilityIcon from "@mui/icons-material/Visibility";
import VisibilityOffIcon from "@mui/icons-material/VisibilityOff";
import RefreshIcon from "@mui/icons-material/Refresh";
import Page from "../../components/Page";
import Layout from "../../layout";

// Mobile-first responsive container
const StyledContainer = styled(Container)(({ theme }) => ({
  paddingTop: theme.spacing(8),
  paddingBottom: theme.spacing(4),
  display: "flex",
  flexDirection: "column",
  gap: theme.spacing(2),
  [theme.breakpoints.up('md')]: {
    paddingTop: theme.spacing(12),
  },
}));

// Mobile-friendly stats grid
const StatsGrid = styled(Box)(({ theme }) => ({
  display: "grid",
  gridTemplateColumns: "repeat(2, 1fr)",
  gap: theme.spacing(1),
  textAlign: "center",
  [theme.breakpoints.up('sm')]: {
    gridTemplateColumns: "repeat(4, 1fr)",
    gap: theme.spacing(2),
  },
}));

// Mobile-optimized action buttons
const MobileActionButton = styled(Button)(({ theme }) => ({
  minHeight: 48, // Touch-friendly height
  fontSize: '0.875rem',
  [theme.breakpoints.down('sm')]: {
    minHeight: 44,
    fontSize: '0.8rem',
  },
}));

// Responsive table container
const ResponsiveTableContainer = styled(TableContainer)(({ theme }) => ({
  marginTop: theme.spacing(2),
  overflowX: "auto",
  // Hide scrollbar on mobile for cleaner look
  '&::-webkit-scrollbar': {
    height: 4,
  },
  '&::-webkit-scrollbar-track': {
    backgroundColor: 'transparent',
  },
  '&::-webkit-scrollbar-thumb': {
    backgroundColor: theme.palette.divider,
    borderRadius: 2,
  },
}));

// Mobile-friendly user card for small screens
const UserCard = styled(Card)(({ theme }) => ({
  marginBottom: theme.spacing(1),
  '& .MuiCardContent-root': {
    padding: theme.spacing(2),
    '&:last-child': {
      paddingBottom: theme.spacing(2),
    },
  },
}));

export default function UserManage() {
  const theme = useTheme();
  const isMobile = useMediaQuery(theme.breakpoints.down('md'));
  const isSmallMobile = useMediaQuery(theme.breakpoints.down('sm'));

  // Core data states
  const [stats, setStats] = useState({
    totalUsers: 0,
    expiredUsers: 0,
    notExpiredUsers: 0,
    todayExpiringUsers: 0,
  });
  const [searchQuery, setSearchQuery] = useState("");
  const [searchResponse, setSearchResponse] = useState(null);

  // Loading states
  const [isLoadingStats, setIsLoadingStats] = useState(true);
  const [isSearching, setIsSearching] = useState(false);
  const [isAddingUser, setIsAddingUser] = useState(false);
  const [isDeletingUser, setIsDeletingUser] = useState(false);
  const [isDeletingDevice, setIsDeletingDevice] = useState(false);
  const [isExtendingLicense, setIsExtendingLicense] = useState(false);

  // Dialog states
  const [openDeleteDialog, setOpenDeleteDialog] = useState(false);
  const [selectedUser, setSelectedUser] = useState(null);
  const [openDeleteDeviceDialog, setOpenDeleteDeviceDialog] = useState(false);
  const [selectedDevice, setSelectedDevice] = useState(null);
  const [openAddDeviceDialog, setOpenAddDeviceDialog] = useState(false);
  const [newDeviceNumber, setNewDeviceNumber] = useState("");
  const [isAddingDevice, setIsAddingDevice] = useState(false);
  const [openRoleDialog, setOpenRoleDialog] = useState(false);
  const [selectedRole, setSelectedRole] = useState("");
  const [isUpdatingRole, setIsUpdatingRole] = useState(false);

  // License extension states
  const [extendUser, setExtendUser] = useState(null);
  const [openExtendDialog, setOpenExtendDialog] = useState(false);
  const [expiredDate, setExpiredDate] = useState("");
  const [licenseKey, setLicenseKey] = useState("");

  // UI states
  const [notification, setNotification] = useState({
    open: false,
    message: "",
    severity: "success",
  });
  const [showPincode, setShowPincode] = useState({});

  // Utility functions with useCallback for performance
  const togglePincodeVisibility = useCallback((userId) => {
    setShowPincode(prev => ({
      ...prev,
      [userId]: !prev[userId]
    }));
  }, []);

  const showNotification = useCallback((message, severity = "success") => {
    setNotification({ open: true, message, severity });
  }, []);

  const hideNotification = useCallback(() => {
    setNotification(prev => ({ ...prev, open: false }));
  }, []);

  // Phone number masking for privacy
  const maskPhoneNumber = useCallback((phoneNumber) => {
    if (!phoneNumber || phoneNumber.length < 4) return phoneNumber;
    const visibleDigits = phoneNumber.slice(-4);
    const maskedPart = '*'.repeat(Math.max(0, phoneNumber.length - 4));
    return maskedPart + visibleDigits;
  }, []);

  // Format days remaining with color coding
  const formatDaysRemaining = useCallback((expiredDate) => {
    const days = Math.ceil((new Date(expiredDate) - new Date()) / (1000 * 60 * 60 * 24));
    return {
      days,
      color: days < 0 ? 'error' : days <= 7 ? 'warning' : 'success',
      text: days < 0 ? `Expired ${Math.abs(days)} days ago` : `${days} days left`
    };
  }, []);

  const handleAddUserSubmit = useCallback(async (event) => {
    event.preventDefault();
    const phoneNumber = event.target.phoneNumber.value.trim();

    if (!phoneNumber) {
      showNotification("Please enter a phone number", "error");
      return;
    }

    // Basic phone number validation
    if (phoneNumber.length < 8) {
      showNotification("Please enter a valid phone number", "error");
      return;
    }

    setIsAddingUser(true);
    try {
      const response = await axios.post("/api/auth/register", { phoneNumber });
      if (response.data.success) {
        showNotification("User added successfully", "success");
        event.target.reset(); // Clear the form
        // Refresh stats
        loadStats();
      } else {
        showNotification(response.data.message || "Failed to add user", "error");
      }
    } catch (error) {
      logger.error("Registration error:", error);
      showNotification("Failed to add user. Please try again.", "error");
    } finally {
      setIsAddingUser(false);
    }
  }, [showNotification]);

  const handleDeleteUser = useCallback(async (phoneNumber) => {
    if (!phoneNumber) return;

    setIsDeletingUser(true);
    try {
      const response = await axios.post("/api/admin/user/delete", { phoneNumber });
      if (response.data.success) {
        showNotification("User deleted successfully", "success");
        // Refresh data
        loadStats();
        if (searchQuery) {
          handleSearch();
        }
      } else {
        showNotification(response.data.message || "Failed to delete user", "error");
      }
    } catch (error) {
      logger.error("Error deleting user:", error);
      showNotification("Failed to delete user. Please try again.", "error");
    } finally {
      setIsDeletingUser(false);
    }
  }, [showNotification, searchQuery]);

  const handleDeleteDevice = useCallback(async (deviceNumber) => {
    if (!deviceNumber) return;

    setIsDeletingDevice(true);
    try {
      const response = await axios.post("/api/device/delete", { deviceNumber });
      if (response.data.success) {
        showNotification("Device deleted successfully", "success");
        // Refresh data
        if (searchQuery) {
          handleSearch();
        }
      } else {
        showNotification(response.data.message || "Failed to delete device", "error");
      }
    } catch (error) {
      logger.error("Error deleting device:", error);
      showNotification("Failed to delete device. Please try again.", "error");
    } finally {
      setIsDeletingDevice(false);
    }
  }, [showNotification, searchQuery]);

  const handleAddDevice = useCallback(async (event) => {
    event.preventDefault();
    if (!selectedUser || !newDeviceNumber.trim()) return;

    setIsAddingDevice(true);
    try {
      const response = await axios.post("/api/device/admin-create", {
        deviceNumber: newDeviceNumber.trim(),
        phoneNumber: selectedUser.phoneNumber,
        uix: "CarV1.2",
        type: "4g",
        isDefault: true,
        deviceName: `Device for ${selectedUser.phoneNumber}`
      });

      if (response.data.success) {
        showNotification("Device created successfully", "success");
        setNewDeviceNumber("");
        setOpenAddDeviceDialog(false);
        // Refresh data
        if (searchQuery) {
          handleSearch();
        }
      } else {
        showNotification(response.data.message || "Failed to create device", "error");
      }
    } catch (error) {
      logger.error("Error creating device:", error);
      showNotification("Failed to create device. Please try again.", "error");
    } finally {
      setIsAddingDevice(false);
    }
  }, [selectedUser, newDeviceNumber, showNotification, searchQuery]);

  const handleExtendLicense = useCallback(async () => {
    if (!extendUser || !expiredDate) {
      showNotification("Please fill in all required fields", "error");
      return;
    }

    // Validate date is in the future
    const selectedDate = new Date(expiredDate);
    const today = new Date();
    if (selectedDate <= today) {
      showNotification("Please select a future date", "error");
      return;
    }

    setIsExtendingLicense(true);
    try {
      const response = await axios.post("/api/admin/user/extend-license", {
        expired: expiredDate,
        user: extendUser._id,
        licenseKey,
      });
      if (response.data.success) {
        showNotification("License extended successfully", "success");
        // Refresh data
        loadStats();
        if (searchQuery) {
          handleSearch();
        }
      } else {
        showNotification(response.data.message || "Failed to extend license", "error");
      }
    } catch (error) {
      logger.error("Error extending license:", error);
      showNotification("Failed to extend license. Please try again.", "error");
    } finally {
      setIsExtendingLicense(false);
      setOpenExtendDialog(false);
      setExtendUser(null);
      setExpiredDate("");
      setLicenseKey("");
    }
  }, [extendUser, expiredDate, licenseKey, showNotification, searchQuery]);

  const handleSearch = useCallback(async () => {
    const trimmedQuery = searchQuery.trim();
    if (!trimmedQuery) {
      showNotification("Please enter a search term", "warning");
      return;
    }

    setIsSearching(true);
    try {
      // Decide whether to treat the query as phoneNumber or deviceNumber based on length
      const paramObj = {};
      if (trimmedQuery.length >= 14) {
        paramObj.deviceNumber = trimmedQuery;
      } else {
        paramObj.phoneNumber = trimmedQuery;
      }

      const response = await axios.get("/api/admin/user/list", { params: paramObj });

      if (response.data.success) {
        // Remove duplicates by comparing phoneNumber
        const uniqueUsers = response.data.users.reduce((acc, user) => {
          if (!acc.some((u) => u.phoneNumber === user.phoneNumber)) {
            acc.push(user);
          }
          return acc;
        }, []);

        setSearchResponse({ ...response.data, users: uniqueUsers });

        if (uniqueUsers.length === 0) {
          showNotification("No users found matching your search", "info");
        }
      } else {
        showNotification("Search failed. Please try again.", "error");
      }
    } catch (error) {
      logger.error("Error searching users:", error);
      showNotification("Search failed. Please check your connection and try again.", "error");
    } finally {
      setIsSearching(false);
    }
  }, [searchQuery, showNotification]);

  const handleUpdateRole = useCallback(async () => {
    if (!selectedUser || !selectedRole) return;

    setIsUpdatingRole(true);
    try {
      const response = await axios.post("/api/admin/user/set-role", {
        phoneNumber: selectedUser.phoneNumber,
        role: selectedRole
      });

      if (response.data.success) {
        showNotification(`User role updated to ${selectedRole} successfully`, "success");
        setSelectedRole("");
        setOpenRoleDialog(false);
        // Refresh search if there's a query
        if (searchQuery) {
          handleSearch();
        }
      } else {
        showNotification(response.data.message || "Failed to update role", "error");
      }
    } catch (error) {
      logger.error("Error updating role:", error);
      showNotification("Failed to update role. Please try again.", "error");
    } finally {
      setIsUpdatingRole(false);
    }
  }, [selectedUser, selectedRole, showNotification, searchQuery, handleSearch]);

  // Load stats function
  const loadStats = useCallback(async () => {
    setIsLoadingStats(true);
    try {
      const response = await axios.get("/api/admin/user/list");
      if (response.data.success) {
        setStats({
          totalUsers: response.data.totalUsers,
          expiredUsers: response.data.expiredUsers,
          notExpiredUsers: response.data.notExpiredUsers,
          todayExpiringUsers: response.data.todayExpiringUsers,
        });
      }
    } catch (error) {
      logger.error("Error loading stats:", error);
      showNotification("Failed to load statistics", "error");
    } finally {
      setIsLoadingStats(false);
    }
  }, [showNotification]);

  // Load initial data
  useEffect(() => {
    loadStats();
  }, [loadStats]);

  // Memoized user data processing
  const processedUsers = useMemo(() => {
    if (!searchResponse?.users) return [];

    return searchResponse.users.map((user) => {
      const rawPayload = searchResponse.lastPayload?.find((lp) => lp._id === user._id) || {};
      let parsedPayload = {};
      if (typeof rawPayload.lastPayload === "string") {
        try {
          parsedPayload = JSON.parse(rawPayload.lastPayload);
        } catch (e) {
          // fallback to empty object
        }
      }

      const simLogData = searchResponse.lastSimcardLog?.find((s) => s._id === user._id) || {};
      let simDate = "";
      let simBalance = "";
      if (typeof simLogData.lastSimcardLog === "string") {
        const match = simLogData.lastSimcardLog.match(/([\d.]+).*?(\d{4}\/\d{2}\/\d{2})/);
        if (match) {
          simBalance = match[1];
          simDate = match[2];
        }
      }

      return {
        ...user,
        parsedPayload,
        simDate,
        simBalance,
        lastPayloadCreatedAt: rawPayload.lastPayloadCreatedAt || "",
        daysInfo: formatDaysRemaining(user.expired)
      };
    });
  }, [searchResponse, formatDaysRemaining]);

  // Mobile user card component
  const MobileUserCard = useCallback(({ user }) => (
    <UserCard key={user._id}>
      <CardContent>
        <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'flex-start', mb: 2 }}>
          <Typography variant="h6" component="div">
            {maskPhoneNumber(user.phoneNumber)}
          </Typography>
          <Chip
            label={user.daysInfo.text}
            color={user.daysInfo.color}
            size="small"
          />
        </Box>

        <Grid container spacing={2}>
          <Grid item xs={6}>
            <Typography variant="body2" color="text.secondary">Device</Typography>
            <Typography
              variant="body2"
              sx={{
                fontWeight: 'medium',
                color: user.devices?.deviceNumber ? 'text.primary' : 'text.secondary',
                fontStyle: user.devices?.deviceNumber ? 'normal' : 'italic'
              }}
            >
              {user.devices?.deviceNumber || 'No device assigned'}
            </Typography>
          </Grid>
          <Grid item xs={6}>
            <Typography variant="body2" color="text.secondary">Version</Typography>
            <Typography variant="body2" sx={{ fontWeight: 'medium' }}>
              {user.parsedPayload.ver || 'N/A'}
            </Typography>
          </Grid>
          <Grid item xs={6}>
            <Typography variant="body2" color="text.secondary">SIM Balance</Typography>
            <Typography variant="body2" sx={{ fontWeight: 'medium' }}>
              {user.simBalance || 'N/A'}
            </Typography>
          </Grid>
          <Grid item xs={6}>
            <Typography variant="body2" color="text.secondary">PIN</Typography>
            <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
              <Typography
                variant="body2"
                sx={{
                  fontFamily: 'monospace',
                  fontWeight: 'medium',
                  letterSpacing: showPincode[user._id] ? '0.1em' : '0.3em'
                }}
              >
                {showPincode[user._id] ? (user.pinCode || user.pin || user.pincode || user.PIN || 'No PIN') : '••••'}
              </Typography>
              <IconButton
                size="small"
                onClick={() => togglePincodeVisibility(user._id)}
                sx={{ p: 0.5 }}
              >
                {showPincode[user._id] ? (
                  <VisibilityOffIcon fontSize="small" />
                ) : (
                  <VisibilityIcon fontSize="small" />
                )}
              </IconButton>
            </Box>
          </Grid>
        </Grid>

        <Box sx={{ display: 'flex', gap: 1, mt: 2, flexWrap: 'wrap' }}>
          <Button
            size="small"
            variant="outlined"
            color="primary"
            onClick={() => {
              setExtendUser(user);
              setOpenExtendDialog(true);
            }}
          >
            Extend
          </Button>
          <Button
            size="small"
            variant="outlined"
            color="success"
            onClick={() => {
              setSelectedUser(user);
              setOpenAddDeviceDialog(true);
            }}
          >
            Add Device
          </Button>
          <Button
            size="small"
            variant="outlined"
            color="info"
            onClick={() => {
              setSelectedUser(user);
              setSelectedRole(user.role || "user");
              setOpenRoleDialog(true);
            }}
          >
            Set Role
          </Button>
          <Button
            size="small"
            variant="outlined"
            color="error"
            onClick={() => {
              setSelectedUser(user);
              setOpenDeleteDialog(true);
            }}
          >
            Delete User
          </Button>
          {user.devices?.deviceNumber && (
            <Button
              size="small"
              variant="outlined"
              color="warning"
              onClick={() => {
                setSelectedDevice(user.devices.deviceNumber);
                setOpenDeleteDeviceDialog(true);
              }}
            >
              Delete Device
            </Button>
          )}
        </Box>
      </CardContent>
    </UserCard>
  ), [showPincode, togglePincodeVisibility, maskPhoneNumber]);

  return (
    <Page title="User Manage">
      <StyledContainer>
        <Layout />

        {/* Extend License Dialog */}
        <Dialog
          open={openExtendDialog}
          onClose={() => {
            if (!isExtendingLicense) {
              setOpenExtendDialog(false);
              setExtendUser(null);
              setExpiredDate("");
              setLicenseKey("");
            }
          }}
          maxWidth="sm"
          fullWidth
        >
          <DialogTitle>Extend License</DialogTitle>
          <DialogContent>
            <DialogContentText sx={{ mb: 2 }}>
              Extend license for user: <strong>{extendUser?.phoneNumber}</strong>
            </DialogContentText>
            <TextField
              fullWidth
              type="date"
              label="New Expiration Date"
              InputLabelProps={{ shrink: true }}
              value={expiredDate}
              onChange={(e) => setExpiredDate(e.target.value)}
              sx={{ mt: 2 }}
              disabled={isExtendingLicense}
              inputProps={{
                min: new Date().toISOString().split('T')[0] // Prevent past dates
              }}
            />
            <TextField
              fullWidth
              label="License Key (optional)"
              value={licenseKey}
              onChange={(e) => setLicenseKey(e.target.value)}
              sx={{ mt: 2 }}
              disabled={isExtendingLicense}
              placeholder="Enter license key if required"
            />
          </DialogContent>
          <DialogActions sx={{ p: 2, gap: 1 }}>
            <Button
              onClick={() => {
                setOpenExtendDialog(false);
                setExtendUser(null);
                setExpiredDate("");
                setLicenseKey("");
              }}
              disabled={isExtendingLicense}
              fullWidth={isSmallMobile}
            >
              Cancel
            </Button>
            <Button
              onClick={handleExtendLicense}
              color="primary"
              variant="contained"
              disabled={isExtendingLicense || !expiredDate}
              startIcon={isExtendingLicense ? <CircularProgress size={16} /> : null}
              fullWidth={isSmallMobile}
            >
              {isExtendingLicense ? 'Extending...' : 'Extend License'}
            </Button>
          </DialogActions>
        </Dialog>

        <Card>
          <CardContent>
            <Grid container spacing={2} justifyContent="center" alignItems="flex-end">
              <Grid item xs={12} sm="auto">
                <TextField
                  fullWidth
                  variant="outlined"
                  label="Search by phone or device number"
                  value={searchQuery}
                  onChange={(e) => setSearchQuery(e.target.value)}
                  onKeyDown={(e) => {
                    if (e.key === 'Enter') {
                      handleSearch();
                    }
                  }}
                  disabled={isSearching}
                />
              </Grid>
              <Grid item>
                <MobileActionButton
                  variant="contained"
                  color="primary"
                  onClick={handleSearch}
                  startIcon={isSearching ? <CircularProgress size={16} /> : <SearchIcon />}
                  disabled={isSearching || !searchQuery.trim()}
                >
                  {isSearching ? 'Searching...' : 'Search'}
                </MobileActionButton>
              </Grid>
              <Grid item>
                <IconButton
                  color="primary"
                  onClick={loadStats}
                  disabled={isLoadingStats}
                  title="Refresh statistics"
                >
                  <RefreshIcon />
                </IconButton>
              </Grid>
            </Grid>
          </CardContent>
        </Card>

        <Card sx={{ mt: 2 }}>
          <CardContent>
            <Box
              component="form"
              onSubmit={handleAddUserSubmit}
              sx={{
                display: "flex",
                alignItems: "center",
                gap: 2,
                flexDirection: { xs: 'column', sm: 'row' }
              }}
            >
              <TextField
                fullWidth
                variant="outlined"
                label="New user phone number"
                name="phoneNumber"
                disabled={isAddingUser}
                placeholder="Enter phone number"
                inputProps={{
                  pattern: "[0-9]*",
                  inputMode: "numeric"
                }}
              />
              <MobileActionButton
                type="submit"
                variant="contained"
                color="primary"
                disabled={isAddingUser}
                startIcon={isAddingUser ? <CircularProgress size={16} /> : <AddIcon />}
                sx={{ minWidth: { xs: '100%', sm: 'auto' } }}
              >
                {isAddingUser ? 'Adding...' : 'Add User'}
              </MobileActionButton>
            </Box>
          </CardContent>
        </Card>

        <Card sx={{ mt: 2 }}>
          <CardContent>
            {isLoadingStats ? (
              <Box sx={{ display: 'flex', justifyContent: 'center', py: 2 }}>
                <CircularProgress size={24} />
              </Box>
            ) : (
              <StatsGrid>
                <Box sx={{ textAlign: 'center' }}>
                  <Typography variant="h6" color="text.primary">
                    {stats.totalUsers}
                  </Typography>
                  <Typography variant="body2" color="text.secondary">
                    Total Users
                  </Typography>
                </Box>
                <Box sx={{ textAlign: 'center' }}>
                  <Typography variant="h6" sx={{ color: "error.main" }}>
                    {stats.expiredUsers}
                  </Typography>
                  <Typography variant="body2" color="text.secondary">
                    Expired
                  </Typography>
                </Box>
                <Box sx={{ textAlign: 'center' }}>
                  <Typography variant="h6" sx={{ color: "success.main" }}>
                    {stats.notExpiredUsers}
                  </Typography>
                  <Typography variant="body2" color="text.secondary">
                    Active
                  </Typography>
                </Box>
                <Box sx={{ textAlign: 'center' }}>
                  <Typography variant="h6" sx={{ color: "warning.main" }}>
                    {stats.todayExpiringUsers}
                  </Typography>
                  <Typography variant="body2" color="text.secondary">
                    Expiring Today
                  </Typography>
                </Box>
              </StatsGrid>
            )}
          </CardContent>
        </Card>

        {searchResponse && searchResponse.users?.length > 0 && (
          <>
            {/* Mobile view - Card layout */}
            {isMobile ? (
              <Box sx={{ mt: 2 }}>
                <Typography variant="h6" sx={{ mb: 2 }}>
                  Search Results ({processedUsers.length})
                </Typography>
                {processedUsers.map((user) => (
                  <MobileUserCard key={user._id} user={user} />
                ))}
              </Box>
            ) : (
              /* Desktop view - Table layout */
              <ResponsiveTableContainer component={Paper}>
                <Table>
                  <TableHead>
                    <TableRow>
                      <TableCell>Phone</TableCell>
                      <TableCell>Days</TableCell>
                      <TableCell>Device</TableCell>
                      <TableCell>Last Log</TableCell>
                      <TableCell>Ver</TableCell>
                      <TableCell>Sim</TableCell>
                      <TableCell>Pincode</TableCell>
                      <TableCell>Actions</TableCell>
                    </TableRow>
                  </TableHead>
                  <TableBody>
                    {processedUsers.map((user) => (
                      <TableRow key={user._id}>
                        {/* Clicking phone => delete user */}
                        <TableCell
                          sx={{ cursor: "pointer" }}
                          onClick={() => {
                            setSelectedUser(user);
                            setOpenDeleteDialog(true);
                          }}
                        >
                          {maskPhoneNumber(user.phoneNumber)}
                        </TableCell>
                        {/* Clicking Days => extend license */}
                        <TableCell
                          sx={{ cursor: "pointer" }}
                          onClick={() => {
                            setExtendUser(user);
                            setOpenExtendDialog(true);
                          }}
                        >
                          <Chip
                            label={user.daysInfo.text}
                            color={user.daysInfo.color}
                            size="small"
                          />
                        </TableCell>
                        {/* Device column with add/delete functionality */}
                        <TableCell>
                          {user.devices?.deviceNumber ? (
                            <Box>
                              <Typography
                                variant="body2"
                                sx={{ cursor: "pointer" }}
                                onClick={() => {
                                  setSelectedDevice(user.devices.deviceNumber);
                                  setOpenDeleteDeviceDialog(true);
                                }}
                              >
                                {user.devices.deviceNumber}
                              </Typography>
                              {user.devices?.createdAt && (
                                <Typography variant="caption" display="block" color="text.secondary">
                                  {new Date(user.devices.createdAt).toLocaleDateString()}
                                </Typography>
                              )}
                            </Box>
                          ) : (
                            <Box sx={{ display: 'flex', flexDirection: 'column', alignItems: 'flex-start', gap: 1 }}>
                              <Typography variant="body2" color="text.secondary" sx={{ fontStyle: 'italic' }}>
                                No device assigned
                              </Typography>
                              <Button
                                size="small"
                                variant="outlined"
                                color="success"
                                onClick={() => {
                                  setSelectedUser(user);
                                  setOpenAddDeviceDialog(true);
                                }}
                              >
                                Add Device
                              </Button>
                            </Box>
                          )}
                        </TableCell>
                        <TableCell>
                          <Typography variant="body2">
                            {user.lastPayloadCreatedAt ?
                              new Date(user.lastPayloadCreatedAt).toLocaleDateString() :
                              'No data'
                            }
                          </Typography>
                        </TableCell>
                        <TableCell>{user.parsedPayload.ver || 'N/A'}</TableCell>
                        <TableCell>
                          <Typography variant="body2">
                            {user.simDate && user.simBalance ?
                              `${user.simDate} - ${user.simBalance}` :
                              'No data'
                            }
                          </Typography>
                        </TableCell>
                        <TableCell>
                          <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                            <Typography
                              variant="body2"
                              sx={{
                                fontFamily: 'monospace',
                                minWidth: '60px',
                                letterSpacing: showPincode[user._id] ? '0.1em' : '0.3em'
                              }}
                            >
                              {showPincode[user._id] ? (user.pinCode || user.pin || user.pincode || user.PIN || 'No PIN') : '••••'}
                            </Typography>
                            <IconButton
                              size="small"
                              onClick={() => togglePincodeVisibility(user._id)}
                              sx={{ p: 0.5 }}
                            >
                              {showPincode[user._id] ? (
                                <VisibilityOffIcon fontSize="small" />
                              ) : (
                                <VisibilityIcon fontSize="small" />
                              )}
                            </IconButton>
                          </Box>
                        </TableCell>
                        <TableCell>
                          <Button
                            size="small"
                            variant="outlined"
                            color="info"
                            onClick={() => {
                              setSelectedUser(user);
                              setSelectedRole(user.role || "user");
                              setOpenRoleDialog(true);
                            }}
                          >
                            Set Role
                          </Button>
                        </TableCell>
                      </TableRow>
                    ))}
                  </TableBody>
                </Table>
              </ResponsiveTableContainer>
            )}
          </>
        )}

        {/* Delete user dialog */}
        <Dialog
          open={openDeleteDialog}
          onClose={() => !isDeletingUser && setOpenDeleteDialog(false)}
          maxWidth="sm"
          fullWidth
        >
          <DialogTitle>Delete User</DialogTitle>
          <DialogContent>
            <DialogContentText>
              Are you sure you want to delete user with phone number{' '}
              <strong>{selectedUser?.phoneNumber}</strong>?
              This action cannot be undone.
            </DialogContentText>
          </DialogContent>
          <DialogActions sx={{ p: 2, gap: 1 }}>
            <Button
              onClick={() => setOpenDeleteDialog(false)}
              disabled={isDeletingUser}
              fullWidth={isSmallMobile}
            >
              Cancel
            </Button>
            <Button
              onClick={() => {
                if (selectedUser) {
                  handleDeleteUser(selectedUser.phoneNumber);
                }
                setOpenDeleteDialog(false);
              }}
              color="error"
              variant="contained"
              disabled={isDeletingUser}
              startIcon={isDeletingUser ? <CircularProgress size={16} /> : null}
              fullWidth={isSmallMobile}
            >
              {isDeletingUser ? 'Deleting...' : 'Delete User'}
            </Button>
          </DialogActions>
        </Dialog>

        {/* Delete device dialog */}
        <Dialog
          open={openDeleteDeviceDialog}
          onClose={() => !isDeletingDevice && setOpenDeleteDeviceDialog(false)}
          maxWidth="sm"
          fullWidth
        >
          <DialogTitle>Delete Device</DialogTitle>
          <DialogContent>
            <DialogContentText>
              Are you sure you want to delete device{' '}
              <strong>{selectedDevice}</strong>?
              This will disconnect the device from the user account.
            </DialogContentText>
          </DialogContent>
          <DialogActions sx={{ p: 2, gap: 1 }}>
            <Button
              onClick={() => setOpenDeleteDeviceDialog(false)}
              disabled={isDeletingDevice}
              fullWidth={isSmallMobile}
            >
              Cancel
            </Button>
            <Button
              onClick={() => {
                if (selectedDevice) {
                  handleDeleteDevice(selectedDevice);
                }
                setOpenDeleteDeviceDialog(false);
              }}
              color="error"
              variant="contained"
              disabled={isDeletingDevice}
              startIcon={isDeletingDevice ? <CircularProgress size={16} /> : null}
              fullWidth={isSmallMobile}
            >
              {isDeletingDevice ? 'Deleting...' : 'Delete Device'}
            </Button>
          </DialogActions>
        </Dialog>

        {/* Add Device Dialog */}
        <Dialog
          open={openAddDeviceDialog}
          onClose={() => {
            if (!isAddingDevice) {
              setOpenAddDeviceDialog(false);
              setNewDeviceNumber("");
            }
          }}
          maxWidth="sm"
          fullWidth
        >
          <DialogTitle>
            Add Device for {selectedUser?.phoneNumber}
          </DialogTitle>
          <DialogContent>
            <DialogContentText sx={{ mb: 2 }}>
              Enter the device number to create a new device for this user. The device will be created with the following default values:
            </DialogContentText>
            <Box sx={{ mb: 2, pl: 2 }}>
              <Typography variant="body2" component="div">
                • <strong>Type:</strong> 4g
              </Typography>
              <Typography variant="body2" component="div">
                • <strong>UIX:</strong> CarV1.2
              </Typography>
              <Typography variant="body2" component="div">
                • <strong>Default Device:</strong> Yes
              </Typography>
            </Box>
            <Box component="form" onSubmit={handleAddDevice} sx={{ mt: 1 }}>
              <TextField
                autoFocus
                margin="dense"
                id="deviceNumber"
                label="Device Number"
                type="text"
                fullWidth
                variant="outlined"
                value={newDeviceNumber}
                onChange={(e) => setNewDeviceNumber(e.target.value)}
                required
                disabled={isAddingDevice}
                placeholder="Enter device number"
              />
            </Box>
          </DialogContent>
          <DialogActions sx={{ p: 2, gap: 1 }}>
            <Button
              onClick={() => {
                setOpenAddDeviceDialog(false);
                setNewDeviceNumber("");
              }}
              disabled={isAddingDevice}
              fullWidth={isSmallMobile}
            >
              Cancel
            </Button>
            <Button
              onClick={handleAddDevice}
              color="success"
              variant="contained"
              disabled={isAddingDevice || !newDeviceNumber.trim()}
              startIcon={isAddingDevice ? <CircularProgress size={16} /> : null}
              fullWidth={isSmallMobile}
            >
              {isAddingDevice ? 'Creating...' : 'Create Device'}
            </Button>
          </DialogActions>
        </Dialog>

        {/* Set Role Dialog */}
        <Dialog
          open={openRoleDialog}
          onClose={() => {
            if (!isUpdatingRole) {
              setOpenRoleDialog(false);
              setSelectedRole("");
            }
          }}
          maxWidth="sm"
          fullWidth
        >
          <DialogTitle>
            Set Role for {selectedUser?.phoneNumber}
          </DialogTitle>
          <DialogContent>
            <DialogContentText sx={{ mb: 2 }}>
              Select the role for this user:
            </DialogContentText>
            <FormControl fullWidth>
              <InputLabel id="role-select-label">Role</InputLabel>
              <Select
                labelId="role-select-label"
                value={selectedRole}
                label="Role"
                onChange={(e) => setSelectedRole(e.target.value)}
                disabled={isUpdatingRole}
              >
                <MenuItem value="user">User</MenuItem>
                <MenuItem value="installer">Installer</MenuItem>
                <MenuItem value="admin">Admin</MenuItem>
              </Select>
            </FormControl>
          </DialogContent>
          <DialogActions sx={{ p: 2, gap: 1 }}>
            <Button
              onClick={() => {
                setOpenRoleDialog(false);
                setSelectedRole("");
              }}
              disabled={isUpdatingRole}
              fullWidth={isSmallMobile}
            >
              Cancel
            </Button>
            <Button
              onClick={handleUpdateRole}
              color="info"
              variant="contained"
              disabled={isUpdatingRole || !selectedRole}
              startIcon={isUpdatingRole ? <CircularProgress size={16} /> : null}
              fullWidth={isSmallMobile}
            >
              {isUpdatingRole ? 'Updating...' : 'Update Role'}
            </Button>
          </DialogActions>
        </Dialog>

        {/* Notification Snackbar */}
        <Snackbar
          open={notification.open}
          autoHideDuration={4000}
          onClose={hideNotification}
          anchorOrigin={{
            vertical: isMobile ? 'bottom' : 'top',
            horizontal: 'center'
          }}
          sx={{
            '& .MuiSnackbarContent-root': {
              minWidth: { xs: '90vw', sm: 'auto' }
            }
          }}
        >
          <Alert
            severity={notification.severity}
            onClose={hideNotification}
            variant="filled"
            sx={{ width: '100%' }}
          >
            {notification.message}
          </Alert>
        </Snackbar>

      </StyledContainer>
    </Page>
  );
}
