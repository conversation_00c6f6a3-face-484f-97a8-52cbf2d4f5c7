
import { useSnackbar } from 'notistack';

// @mui 
import { <PERSON><PERSON>, Grid, Switch, Container, Typo<PERSON>, Divider, TextField, Button, Select, MenuItem, FormControl, InputLabel, Box, FormControlLabel } from '@mui/material';
import SatelliteAltIcon from '@mui/icons-material/SatelliteAlt';

import { useState, useEffect, useCallback } from 'react';
import { useNavigate } from 'react-router-dom';
import { useTranslation } from 'react-i18next';
import { Swiper, SwiperSlide } from 'swiper/react';
import { EffectCoverflow, Navigation } from 'swiper';
// hooks
import useAuth from '../hooks/useAuth';
// components
import CarFront from '../components/CarFront';
import Page from '../components/Page';
import axios from '../utils/axios';
import Layout from '../layout';
import { PaymentDialog } from './PaymentDialog';
import BarcodeScannerComponent from "react-qr-barcode-scanner";
import { IconButtonAnimate } from '../components/animate';
import Iconify from '../components/Iconify';

import ChipIcon from '../components/ChipIconi';

// ----------------------------------------------------------------------
const SLIDER_SETTING = {
  effect: "coverflow",
  grabCursor: true,
  centeredSlides: true,
  loop: true,
  pagination: false,
  slidesPerView: 2,
  spaceBetween: 60,
  coverflowEffect: {
    rotate: 0,
    stretch: 0,
    depth: 180,
    modifier: 3,
    slideShadows: true,
  },
  navigation: {
    nextEl: ".swiper-button-next",
    prevEl: ".swiper-button-prev",
  },
}

export default function DeviceRegister() {
  const { initialize, user } = useAuth();
  const [bankList] = useState([]);
  const [mode, setMode] = useState('view');
  const navigate = useNavigate();
  const [paymentRequest, setPaymentRequest] = useState(false);
  const { t } = useTranslation();
  const [viewRentable, setViewRentable] = useState(false);
  const [newDevice, setNewDevice] = useState({ deviceName: "", deviceNumber: "", type: "4g", uix: "Car", _id: '', isDefault: false, rentable: false, });
  const { enqueueSnackbar } = useSnackbar();

  const [devices, setDevices] = useState(user?.devices || []);
  const [useCamera, setUseCamera] = useState(false);
  const handleQrResult = (err, result) => {
    if (result) {
      (setNewDevice({ ...newDevice, deviceNumber: result?.text }));
      // play();
    } else {

    }
  };

  const onSubmit = async () => {
    const exist = user?.devices?.filter((d) => (d.type === newDevice.type && d.deviceNumber === newDevice.deviceNumber && d.uix === newDevice.uix)).length > 0;
    if (newDevice._id === '' && exist) {
      enqueueSnackbar('Alreay exist with this number', { variant: 'error' });
      return;
    }

    const res = await axios.post(`/api/device/register`, { ...newDevice });
    try {
      if (res.data.success) {
        enqueueSnackbar('Device is registered', { variant: 'success' });
        initialize();
      }
      else {
        enqueueSnackbar('Already registered device', { variant: 'error' });
      }
    }
    catch (err) {
      // console.log(err);
    }

  };
  const onAdd = () => {
    setNewDevice({ deviceName: '', deviceNumber: "", type: "4g", uix: "Car", _id: '', isDefault: false, rentable: false })
    setMode('edit')
  }
  const onEdit = () => {
    setMode('edit')
  }
  const onIndexChanged = useCallback((swiper) => {
    let index = swiper.realIndex;
    if (index >= devices?.length) {
      index = 0;
    }
    if (devices[index]) {
      console.log(devices[index]);
      setNewDevice({ ...devices[index], _id: devices[index]._id });
    }
  }, [devices]); // Include devices as the dependency so the function is only recreated when devices change
  
  const onDelete = async () => {
    if (newDevice.isDefault)
      return;
    try {
      const res = await axios.post('/api/device/delete', { deviceNumber: newDevice.deviceNumber });
      if (res.data.success) {
        enqueueSnackbar('Device is Deleted', { variant: 'success' });
      } else {
        enqueueSnackbar(res.data.message || 'Failed to delete device', { variant: 'error' });
      }
    }
    catch (err) {
      console.error('Error deleting device:', err);
      enqueueSnackbar('Failed to delete device', { variant: 'error' });
    }
  }
  useEffect(() => {

    if (mode === 'view')
      initialize();
  }, [mode, initialize])

  useEffect(() => {

    if (viewRentable) {
      axios.get('/api/device/search-rent-cars').then(res => {
        if (res.status === 200 && res.data.success) {
          // console.log(res.data.cars)
          setDevices(res.data.cars);

        }
      })
    }
    else {
      setDevices(user?.devices || []);
    }

  }, [viewRentable, user?.devices])
  useEffect(() => {
    if (devices.length > 0) {
      onIndexChanged({ realIndex: 0 });
    }
  }, [devices, onIndexChanged]);
  return (
    <Page title="Device registration">
      <Layout />
      <Container sx={{ py: { xs: 12 } }} maxWidth={'xs'}>
        <Grid container spacing={3}  >
          {mode === 'view' &&
            <Grid item xs={12} textAlign={"center"}>
              <Box sx={{ position: 'relative', marginBottom: 2 }}>
                <Swiper {...SLIDER_SETTING} modules={[Navigation, EffectCoverflow]} onActiveIndexChange={onIndexChanged}>
                  {devices?.map((device, index) => (
                    <SwiperSlide key={index}>

                      <Box>
                        <Typography variant='h6' sx={{ pt: 4, display: 'flex', alignItems: 'center', justifyContent: 'center' }}>
                          <Iconify icon={'carbon:sim-card'} width={24} height={24}></Iconify>&nbsp;{device?.deviceName || device?.phoneNumber || ' not available'}
                        </Typography>
                        {device?.uix.includes('Car') && <CarFront disabledLink />}
                        {device?.uix === 'Chip' && <Box sx={{ marginX: -2 }}><ChipIcon sx={{ color: 'yellow' }} /></Box>}
                        {device?.uix === 'GPS' &&
                          <Box py={4}>
                            <SatelliteAltIcon sx={{ width: 60, height: 60 }} color={'primary.light'} />

                          </Box>
                        }
                        <Typography variant='subtitle2' sx={{ pt: 1, display: 'flex', alignItems: 'center', justifyContent: 'center' }} color={"grey.500"}>
                          <Iconify icon={device?.isDefault ? "fe:check-verified" : "codicon:unverified"}></Iconify>{device?.deviceNumber}
                        </Typography>
                        <Box sx={{ position: 'absolute', top: '30%', right: '0%', }}>
                          <Iconify color={device?.type === 'sms' ? 'grey.500' : 'red'} icon={device?.type === 'sms' ? 'arcticons:sms-gate' : 'healthicons:network-4g-outline'} width={24} height={24} />
                        </Box>
                      </Box>

                    </SwiperSlide>
                  ))}


                </Swiper>
                <Stack direction='row' pt={{ xs: 1, md: 2 }} justifyContent='center' >
                  <IconButtonAnimate className="swiper-button-prev">
                    <Iconify icon='eva:arrow-back-outline' width={30} height={30} />
                  </IconButtonAnimate>
                  <IconButtonAnimate className="swiper-button-next">

                    <Iconify icon='eva:arrow-forward-outline' width={30} height={30} />
                  </IconButtonAnimate>
                </Stack>
              </Box>

              <Stack direction="row" gap={2} sx={{ py: 4 }}>
                <Button fullWidth size="large" sx={{ border: '1px solid', borderColor: 'grey.50048' }}
                  onClick={onEdit} variant="contained"  >
                  {t("words.edit")}
                </Button>
                <Button fullWidth size="large" sx={{ bgcolor: 'grey.50016', border: '1px solid', borderColor: 'grey.50048' }}
                  onClick={onAdd} variant="contained" >
                  {t("words.register")}
                </Button>
              </Stack>
              {user?.role?.toLowerCase().includes('admin') &&
                <Stack direction="row" justifyContent="center" alignItems={'center'} gap={2}>
                  <FormControlLabel control={<Switch checked={viewRentable} onChange={(e) => {
                    (setViewRentable(e.target.checked))
                  }} />} label={t("words.rentable_device")} />
                </Stack>
              }
            </Grid>
          }
          {mode === 'edit' &&
            <Grid item xs={12}  >

              <Stack direction={'row'} justifyContent={'space-between'} alignItems={'center'}>
                <Typography variant='h4'>
                  {t("device_profile.device_information")}
                </Typography>
                <IconButtonAnimate onClick={() => setMode('view')}>
                  <Iconify icon={'ep:back'} >

                  </Iconify>
                </IconButtonAnimate>

              </Stack>

              <Divider sx={{ mb: 4, mt: 1 }} />

              <Stack spacing={3}>

                <TextField value={newDevice.deviceName} label={t("words.device_name")} onChange={(e) => (setNewDevice({ ...newDevice, deviceName: e.target.value }))} sx={{ flexGrow: 1 }} />

                {/* <Box sx={{ width: '100%', }}>
                  <input accept="image/*" type='file' hidden id='image' onChange={imageChange} />
                  <Typography sx={{ display: 'flex', justifyContent: 'center', alignItems: 'center' }} component="label" htmlFor='image' >
                    {image != '' ?
                      <img src={typeof image === 'object' ? URL.createObjectURL(image) : `${HOST_API}${image}`} style={{ height: 150, width:240 }} />
                      :
                      <img src={`/images/driver-license.png`} style={{ height: 150, width:240  }} />
                    }
                  </Typography>
                </Box> */}

                <Stack direction="row" gap={1}>
                  <TextField value={newDevice.deviceNumber} label={t("words.device_number")} onChange={(e) => (setNewDevice({ ...newDevice, deviceNumber: e.target.value }))} sx={{ flexGrow: 1 }} />
                  <Button variant='contained' onClick={() => { setUseCamera(!useCamera) }}>QR</Button>
                </Stack>
                {useCamera &&
                  <Box sx={{ width: '280px', height: '280px', borderColor: 'white', borderWidth: '1px', display: 'flex', borderStyle: 'solid', justifyContent: 'center', alignItems: 'center' }} >
                    <Box sx={{ width: '100%', height: '100%', overflow: 'hidden' }}>
                      <BarcodeScannerComponent
                        width={276}
                        height={276}
                        onUpdate={handleQrResult}
                        style={{ width: '276px', height: '276px' }}
                      />
                    </Box>
                  </Box>
                }
                <FormControl>
                  <InputLabel id="type-select-label">{t("words.device_type")}</InputLabel>
                  <Select value={newDevice.type} label={t("words.device_type")} onChange={(e) => (setNewDevice({ ...newDevice, type: e.target.value }))} labelId="type-select-label">
                    <MenuItem value="4g">4G</MenuItem>
                    <MenuItem value="sms">SMS</MenuItem>
                  </Select>
                </FormControl>
                <FormControl>
                  <InputLabel id="type-select-label">{t("words.uix_type")}</InputLabel>
                  <Select value={newDevice.uix} label={t("words.uix_type")} onChange={(e) => {
                    setNewDevice({ ...newDevice, uix: e.target.value });

                  }} labelId="type-select-label">
                    <MenuItem value="CarV1.1">CarV 1.1</MenuItem>
                    <MenuItem value="CarV1.2">CarV 1.2</MenuItem>
                    <MenuItem value="Car2.1">Asa2.1</MenuItem>
                    <MenuItem value="Car2.2">Asa2.2</MenuItem>
                    <MenuItem value="Chip">Chip</MenuItem>
                    <MenuItem value="GPS">GPS</MenuItem>
                  </Select>
                </FormControl>

                <Stack direction={'row'} justifyContent={'space-between'} alignItems={'center'}>
                  {user?.role?.toLowerCase()?.includes('admin') &&
                    <FormControlLabel control={<Switch checked={newDevice.rentable} onChange={(e) => {
                      (setNewDevice({ ...newDevice, rentable: e.target.checked }))

                    }} />} label={t("words.rentable_device")} />

                  }
                  <FormControlLabel control={<Switch checked={newDevice.isDefault} onChange={(e) => {
                    (setNewDevice({ ...newDevice, isDefault: e.target.checked }))

                  }} />} label={t("words.default_device")} />

                  {newDevice.uix === 'GPS' &&

                    <Typography onClick={() => navigate(`/configure-gps/${newDevice.deviceNumber}`, { replace: true })}>
                      {t('words.details')}
                    </Typography>


                  }
                </Stack>

                <Stack direction={'row'} gap={2}>
                  <Button fullWidth size="large" sx={{ bgcolor: 'grey.50016', border: '1px solid', borderColor: 'grey.50048' }}
                    onClick={onSubmit} variant="contained" >
                    {newDevice._id !== '' ? t("words.update") : t("words.register")}

                  </Button>
                  <Button fullWidth size="large" sx={{ bgcolor: 'grey.50016', border: '1px solid', borderColor: 'grey.50048' }}
                    onClick={onDelete} variant="contained" disabled={newDevice.isDefault || newDevice._id === ''}>
                    {t('words.delete')}

                  </Button>
                </Stack>

              </Stack>
            </Grid>
          }
        </Grid>
      </Container>
      {paymentRequest && <PaymentDialog open={paymentRequest} onClose={() => { initialize(); setPaymentRequest(false); }} bankList={bankList} />}
    </Page>
  );
}
