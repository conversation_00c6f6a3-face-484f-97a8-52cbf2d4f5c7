import PropTypes from 'prop-types';
import { Navigate } from 'react-router-dom';
// hooks
import useAuth from '../hooks/useAuth';
// components
import LoadingScreen from '../components/LoadingScreen';

// ----------------------------------------------------------------------

AdminOrInstallerGuard.propTypes = {
  children: PropTypes.node
};

export default function AdminOrInstallerGuard({ children }) {
  const { isAuthenticated, isInitialized, user } = useAuth();

  if (!isInitialized) {
    return <LoadingScreen />;
  }

  if (!isAuthenticated) {
    return <Navigate to="/auth/login" />;
  }

  if (!user?.role?.includes("admin") && !user?.role?.includes("installer")) {
    return <Navigate to="/" />;
  }

  return <>{children}</>;
}
