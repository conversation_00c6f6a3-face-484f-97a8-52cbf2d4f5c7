const mqtt = require("mqtt");
const device = require("../models/device");
const GpsLogModel = require("../models/gpslog");
const carGps = require("../models/carGps");
const { sendMessageToChannel } = require("./socket");
const DeviceMessageLogModel = require("../models/deviceMessageLog");
const MopedMessageLogModel = require("../models/mopedMessageLog");
const SimcardSmsLogModel = require("../models/simSmsLog");
const firebaseAdmin = require("firebase-admin");

const url = "mqtt://45.76.188.38:1883";
// const url = "mqtt://127.0.0.1:1883";
const voltFlag = {}; // This will store the state of the volt for each device

let client = null;

const messaging = firebaseAdmin.messaging();

const REQUIRED_DEVICE_LENGTH = 15; // The required length for device numbers

const subscribe = (imei) => {
  if (client != null && client.connected) {
    client.subscribe([`${imei}/post`], () => {
      console.log(`subscribe ... ${imei}`);
    });
    return true;
  } else {
    return false;
  }
};

const subscribeCar2 = (imei) => {
  if (client != null && client.connected) {
    client.subscribe([`${imei}/msg`], () => {
      // console.log(`subscribe car2... ${imei}`);
    });
    return true;
  } else {
    return false;
  }
};

const unsubscribeCar2 = (imei) => {
  if (client != null && client.connected) {
    client.unsubscribe([`${imei}/msg`], () => {
      console.log(`unsubscribe car2... ${imei}`);
    });
    return true;
  } else {
    return false;
  }
};

const unsubscribe = (imei) => {
  console.log("..unsubscribing...");
  if (client != null && client.connected) {
    client.unsubscribe([`${imei}/post`], () => {
      console.log(`unsubscribe ... ${imei}`);
    });
    return true;
  } else {
    return false;
  }
};

const publish = (data, isHex = true) => {
  if (client != null && client.connected) {
      return new Promise((resolve, reject) => {
          if (isHex) {
              const hex = Buffer.from(data.payload, 'hex');

              client.publish(data.topic, hex.toString('ascii'), { qos: data.qos, retain: data.retain }, (err) => {
                  if (!err) {
                      return resolve('success');
                  } else {
                      return reject('failed');
                  }
              });
          } else {
              try {
                  let payload = `{"id":"${data.topic}","command":"${data.payload}"}`;
                  if (data.timer) {
                      payload = `{"id":"${data.topic}","command":"${data.payload}","timer":${(data.timer)}}`
                  }
                  if (data.temp) {
                      payload = `{"id":"${data.topic}","command":"${data.payload}","temp":"${data.temp[0]}x${data.temp[1]}"}`
                  }
                  client.publish(data.topic, payload, { qos: data.qos, retain: data.retain }, (err) => {
                      if (!err) {
                          return resolve('success');
                      } else {
                          console.log(err)
                          return reject('failed');
                      }
                  });
              } catch (err) {
                  console.log(err)
                  return reject('failed');
              }
              //


          }
      })
  } else {
      return 'failed'
  }

} 

const schedpublish = async (message, retain = false) => {
  return new Promise((resolve, reject) => {
    try {
      if (!client || !client.connected) {
        console.error("MQTT client not connected");
        return resolve("mqtt-not-connected");
      }

      const { topic, payload, qos = 0 } = message;
      
      // Ensure payload is a string
      const stringPayload = typeof payload === 'object' ? JSON.stringify(payload) : payload;
      
      console.log(`Publishing to ${topic}: ${stringPayload}`);
      
      client.publish(topic, stringPayload, { qos, retain }, (err) => {
        if (err) {
          console.error(`Error publishing to ${topic}:`, err);
          return resolve("error");
        }
        
        console.log(`Successfully published to ${topic}`);
        return resolve("success");
      });
    } catch (error) {
      console.error("Error in schedpublish:", error);
      return resolve("error");
    }
  });
};

// Store command response handlers
const commandResponseHandlers = new Map();

// Register a command response handler
const registerCommandResponseHandler = (deviceNumber, handler) => {
  commandResponseHandlers.set(deviceNumber, handler);
  
  // Set a timeout to automatically remove the handler after 15 seconds
  setTimeout(() => {
    if (commandResponseHandlers.has(deviceNumber)) {
      console.log(`Removing stale command response handler for device ${deviceNumber}`);
      commandResponseHandlers.delete(deviceNumber);
    }
  }, 15000);
};

// Remove a command response handler
const removeCommandResponseHandler = (deviceNumber) => {
  commandResponseHandlers.delete(deviceNumber);
};

const initializeMqttClient = () => {
  console.log("...connecting to MQTT.....");
  client = mqtt.connect(url, {});

  client.on("disconnect", () => {
    console.log("disconnected");
  });

  client.on("connect", async () => {
    console.log("connected to MQTT server");
    // Fetch all devices and subscribe to their msg topics
    try {
      const allDevices = await device.find({});

      allDevices.forEach((dev) => {
        const imei = dev.deviceNumber;
        if (imei && imei.length === REQUIRED_DEVICE_LENGTH) {
          subscribeCar2(imei);
        }
      });

      console.log("Successfully subscribed to all device topics");
    } catch (err) {
      console.error("Error subscribing devices:", err);
    }
  });

  const motionFlag = {}; // Flag to track motion for each device
  const speedFlag = {};  // Flag to track speed for each device
  
  client.on("message", async (topic, payload) => {
    if (topic.toLowerCase().includes("/msg")) {
      const deviceNumber = topic.replace("/msg", "");
      let response = payload.toString();
      
      // Sanitize the response (remove unwanted characters, control chars, etc.)
      const sanitizedResponse = response
        .replace(/[\u0000-\u001F\u007F-\u009F\u00AD\u0600-\u0604\u070F\u17B4\u17B5\u200C-\u200F\u2028-\u202F\u2060-\u206F\uFEFF\uFFF0-\uFFFF]/g, '')
        .replace(/\\n/g, "")
        .replace(/[\r\n]+$/, '')
        .trim();
      
      // Check if there's a registered command response handler for this device
      const hasCommandHandler = commandResponseHandlers.has(deviceNumber);
      
      // If there's a command handler, call it with the response
      if (hasCommandHandler) {
        console.log(`Found command response handler for device ${deviceNumber}`);
        const handler = commandResponseHandlers.get(deviceNumber);
        
        try {
          // Parse the response for the command handler
          let parsedResponse;
          try {
            parsedResponse = JSON.parse(sanitizedResponse);
          } catch (e) {
            parsedResponse = { rawPayload: sanitizedResponse };
          }
          
          // Call the handler with the parsed response
          handler(parsedResponse);
          
          // Remove the handler after it's been called
          commandResponseHandlers.delete(deviceNumber);
          
          console.log(`Command response handler for device ${deviceNumber} executed and removed`);
        } catch (err) {
          console.error(`Error executing command response handler for device ${deviceNumber}:`, err);
        }
      }

      const foundDevice = await device.findOne({ deviceNumber });
      if (!foundDevice) {
        console.log(`Device with number ${deviceNumber} not found`);
        return;
      }
      
      // Try to parse the message as JSON
      try {
        const parsedMessage = JSON.parse(sanitizedResponse);
        
        // Check if the message contains server information
        if (parsedMessage.server && parsedMessage.id === deviceNumber) {
          console.log(`Updating device ${deviceNumber} renter to ${parsedMessage.server}`);
          
          // Update the device's renter field with the server value
          await device.findOneAndUpdate(
            { deviceNumber },
            { renter: parsedMessage.server }
          );
          
          console.log(`Device ${deviceNumber} renter updated to ${parsedMessage.server}`);
        }
      } catch (err) {
        // If parsing fails, continue with normal message processing
        console.log(`Could not parse message as JSON for device ${deviceNumber}`);
      }

      // Create a safe message object that doesn't rely on JSON parsing
      const msgObject = { payload: sanitizedResponse };
      
      if (msgObject && msgObject.payload) {
        try {
          // Try parsing the sanitized payload
          const parsedPayload = JSON.parse(sanitizedResponse);
          const { volt, motion, Speed } = parsedPayload;
          const deviceFlag = voltFlag[deviceNumber];
          const motionDeviceFlag = motionFlag[deviceNumber];
          const speedDeviceFlag = speedFlag[deviceNumber];

          // Process SMS content if present
          if (parsedPayload.sms) {
            console.log(`SMS found for device ${deviceNumber}:`, parsedPayload.sms);
            await handleSmsContent(deviceNumber, parsedPayload.sms);
          }
          
          // Rest of the voltage, motion, and speed handling...
          
        } catch (err) {
          console.error("Error parsing payload for device", deviceNumber, ":", err);
          // Continue with the flow even if parsing fails
        }
      }

      // Handle GPS content with our improved function
      await handleGpsContent(deviceNumber, sanitizedResponse, foundDevice);
      
      // Send message to channel with the raw payload to avoid parsing issues
      // Pass isCommandResponse flag to indicate if this is a command response
      sendMessageToChannel(deviceNumber, msgObject, "4g", hasCommandHandler);
    }
  });
};

// Helper function to send Firebase messages
const sendMessageFirebase = async (message, fmcToken) => {
  if (!fmcToken) {
    console.log("No FCM token provided, skipping message send.");
    return;
  }

  try {
    const messagePayload = {
      notification: {
        title: "Машин",
        body: message,
      },
      token: fmcToken,
    };
    await messaging.send(messagePayload);
    console.log("Firebase message sent successfully");
  } catch (error) {
    // console.error("Error sending Firebase message:", error);
  }
};

const handleSmsContent = async (deviceNumber, content) => {
  try {
    let model = new SimcardSmsLogModel({
      deviceNumber: deviceNumber,
      content,
    });

    // Attempt to parse numeric balance from the content.
    // Formats: "XXXX.XX TG" or "XXXX tug"
    const balanceRegex = /(\d+(\.\d+)?)(\s*tug|\s*TG)?/i;
    const balanceMatch = content.match(balanceRegex);

    let balance = null;
    if (balanceMatch && balanceMatch[1]) {
      balance = balanceMatch[1];
    }

    // Attempt to parse date from the content.
    // Considering both YYYY/MM/DD and DD-MM-YYYY formats.
    const dateRegexes = [
      /\d{4}\/\d{2}\/\d{2}/,    // YYYY/MM/DD
      /\d{2}-\d{2}-\d{4}/       // DD-MM-YYYY
    ];

    let dateValue = null;
    for (const regex of dateRegexes) {
      const match = content.match(regex);
      if (match && match[0]) {
        dateValue = match[0];
        break;
      }
    }

    // Assign balance if found
    if (balance !== null) {
      model.balance = balance;
    }

    // If a date was found, convert it to a standardized Date object
    if (dateValue) {
      let dateObj = null;
      if (dateValue.includes('/')) {
        // Format: YYYY/MM/DD
        dateObj = new Date(dateValue.replace(/\//g, '-')); 
      } else if (dateValue.includes('-')) {
        // Format: DD-MM-YYYY
        const parts = dateValue.split('-');
        const reformatted = `${parts[2]}-${parts[1]}-${parts[0]}`; 
        dateObj = new Date(reformatted);
      }

      if (dateObj && !isNaN(dateObj.getTime())) {
        model.expired = dateObj;
      }
    }

    await model.save();
    console.log("SMS log saved successfully");
  } catch (err) {
    console.error("Error saving SMS log:", err);
  }
};

const handleGpsContent = async (deviceNumber, response, foundDevice) => {
  try {
    // Sanitize the response to handle potential encoding issues
    const sanitizedResponse = response
      .replace(/[\u0000-\u001F\u007F-\u009F\u00AD\u0600-\u0604\u070F\u17B4\u17B5\u200C-\u200F\u2028-\u202F\u2060-\u206F\uFEFF\uFFF0-\uFFFF]/g, '')
      .trim();
    
    // Try to parse the sanitized JSON
    let content;
    try {
      content = JSON.parse(sanitizedResponse);
    } catch (parseError) {
      console.log(`Invalid JSON for device ${deviceNumber}, saving raw payload instead`);
      // If parsing fails, save the raw payload instead
      content = { rawPayload: sanitizedResponse };
    }

    const payloadToSave = {
      deviceNumber,
      payload: JSON.stringify(content),
      deviceType: foundDevice.type,
    };

    await carGps.create(payloadToSave);
    // console.log(`GPS data saved for device ${deviceNumber}`);
  } catch (err) {
    console.error("Error logging GPS data:", err);
  }
};

// Function to publish scheduled commands with response handling
const schedpublishWithResponse = async (message, timeout = 10000) => {
  return new Promise((resolve, reject) => {
    try {
      if (!client || !client.connected) {
        console.error("MQTT client not connected");
        return resolve({ success: false, error: "mqtt-not-connected" });
      }

      const { topic, payload, qos = 0 } = message;
      
      // Ensure payload is a string
      const stringPayload = typeof payload === 'object' ? JSON.stringify(payload) : payload;
      
      console.log(`Publishing to ${topic} with response handling: ${stringPayload}`);
      
      // Register a response handler for this device
      registerCommandResponseHandler(topic, (response) => {
        console.log(`Received response for command to ${topic}:`, response);
        resolve({ success: true, response });
      });
      
      // Set a timeout to resolve with failure if no response is received
      const timeoutId = setTimeout(() => {
        if (commandResponseHandlers.has(topic)) {
          console.log(`Command to ${topic} timed out after ${timeout}ms`);
          commandResponseHandlers.delete(topic);
          resolve({ success: false, error: "timeout" });
        }
      }, timeout);
      
      // Publish the message
      client.publish(topic, stringPayload, { qos, retain: false }, (err) => {
        if (err) {
          console.error(`Error publishing to ${topic}:`, err);
          clearTimeout(timeoutId);
          commandResponseHandlers.delete(topic);
          return resolve({ success: false, error: err.message });
        }
        
        console.log(`Successfully published to ${topic}`);
        // Now waiting for response or timeout
      });
    } catch (error) {
      console.error("Error in schedpublishWithResponse:", error);
      return resolve({ success: false, error: error.message });
    }
  });
};

// Make sure to call initializeMqttClient when the module is loaded
initializeMqttClient();

// Export the client instance and functions
module.exports = {
  initializeMqttClient,
  subscribe,
  unsubscribe,
  subscribeCar2,
  unsubscribeCar2,
  publish,
  schedpublish,
  schedpublishWithResponse,
  registerCommandResponseHandler,
  removeCommandResponseHandler,
  client // Export the client instance
};
