{"version": 3, "sources": ["pages/auth/ForgotPassword.js", "../node_modules/@babel/runtime/helpers/esm/objectWithoutProperties.js", "components/Page.js", "../node_modules/@mui/system/esm/styled.js", "../node_modules/@mui/system/esm/Container/createContainer.js", "../node_modules/@mui/material/Container/Container.js", "../node_modules/@mui/material/Typography/typographyClasses.js", "../node_modules/@mui/material/Typography/Typography.js"], "names": ["CarFront", "lazy", "ForgotPasswordForm", "ContentStyle", "styled", "_ref", "theme", "max<PERSON><PERSON><PERSON>", "margin", "display", "minHeight", "flexDirection", "justifyContent", "align<PERSON><PERSON><PERSON>", "gap", "ForgotPassword", "_jsx", "Page", "title", "children", "_jsxs", "Typography", "variant", "gutterBottom", "textAlign", "Box", "width", "sx", "mx", "mb", "mt", "paragraph", "my", "_objectWithoutProperties", "e", "t", "o", "r", "i", "Object", "getOwnPropertySymbols", "n", "length", "indexOf", "propertyIsEnumerable", "call", "forwardRef", "ref", "meta", "other", "_excluded", "_Fragment", "<PERSON><PERSON><PERSON>", "_objectSpread", "Container", "propTypes", "PropTypes", "node", "isRequired", "string", "createStyled", "defaultTheme", "createTheme", "defaultCreateStyledComponent", "systemStyled", "name", "slot", "overridesResolver", "props", "styles", "ownerState", "root", "concat", "capitalize", "String", "fixed", "disableGutters", "useThemePropsDefault", "inProps", "useThemePropsSystem", "useUtilityClasses", "componentName", "classes", "slots", "composeClasses", "generateUtilityClass", "options", "arguments", "undefined", "createStyledComponent", "useThemeProps", "ContainerRoot", "_extends", "marginLeft", "boxSizing", "marginRight", "paddingLeft", "spacing", "paddingRight", "breakpoints", "up", "_ref2", "keys", "values", "reduce", "acc", "breakpoint<PERSON><PERSON><PERSON><PERSON><PERSON>", "breakpoint", "value", "unit", "_ref3", "Math", "max", "xs", "React", "className", "component", "_objectWithoutPropertiesLoose", "as", "clsx", "createContainer", "getTypographyUtilityClass", "generateUtilityClasses", "typographyClasses", "TypographyRoot", "align", "noWrap", "typography", "overflow", "textOverflow", "whiteSpace", "marginBottom", "defaultVariantMapping", "h1", "h2", "h3", "h4", "h5", "h6", "subtitle1", "subtitle2", "body1", "body2", "inherit", "colorTransformations", "primary", "textPrimary", "secondary", "textSecondary", "error", "themeProps", "color", "transformDeprecatedColors", "extendSxProp", "variantMapping", "Component"], "mappings": "oGAAA,oGAUA,MAAMA,EAAWC,gBAAK,IAAM,iCACtBC,EAAqBD,gBAAK,IAAM,6EAIhCE,EAAeC,YAAO,MAAPA,EAAcC,IAAA,IAAC,MAAEC,GAAOD,EAAA,MAAM,CACjDE,SAAU,IACVC,OAAQ,OACRC,QAAS,OACTC,UAAW,QACXC,cAAe,SACfC,eAAgB,SAChBC,aAAc,gBACdC,IAAK,EACN,IAIc,SAASC,IACtB,OACEC,cAACC,IAAI,CAACC,MAAM,kBAAiBC,SAC3BC,eAACjB,EAAY,CAAAgB,SAAA,CACXH,cAACK,IAAU,CAACC,QAAQ,KAAKC,cAAY,EAACC,UAAW,SAASL,SAAC,wGAI3DH,cAACS,IAAG,CAACC,MAAO,MAAOC,GAAI,CAAEC,GAAI,OAAQC,GAAI,EAAGC,IAAK,GAAIX,SACnDH,cAAChB,EAAQ,MAIXgB,cAACK,IAAU,CAACU,WAAS,EAACP,UAAW,SAASL,SAAC,4rCAG3CC,eAACK,IAAG,CAACC,MAAO,MAAOE,GAAI,OAAQI,GAAI,EAAEb,SAAA,CACnCH,cAACd,EAAkB,IAAG,WAKhC,C,mCClDA,8CACA,SAAS+B,EAAyBC,EAAGC,GACnC,GAAI,MAAQD,EAAG,MAAO,CAAC,EACvB,IAAIE,EACFC,EACAC,EAAI,YAA6BJ,EAAGC,GACtC,GAAII,OAAOC,sBAAuB,CAChC,IAAIC,EAAIF,OAAOC,sBAAsBN,GACrC,IAAKG,EAAI,EAAGA,EAAII,EAAEC,OAAQL,IAAKD,EAAIK,EAAEJ,IAAK,IAAMF,EAAEQ,QAAQP,IAAM,CAAC,EAAEQ,qBAAqBC,KAAKX,EAAGE,KAAOE,EAAEF,GAAKF,EAAEE,GAClH,CACA,OAAOE,CACT,C,oJCHMrB,EAAO6B,sBAAW,CAAAzC,EAA2C0C,KAAG,IAA7C,SAAE5B,EAAQ,MAAED,EAAQ,GAAE,KAAE8B,GAAgB3C,EAAP4C,EAAKhB,YAAA5B,EAAA6C,GAAA,OAC7D9B,eAAA+B,WAAA,CAAAhC,SAAA,CACEC,eAACgC,IAAM,CAAAjC,SAAA,CACLH,cAAA,SAAAG,SAAQD,IACP8B,KAGHhC,cAACS,IAAG4B,wBAAA,CAACN,IAAKA,GAASE,GAAK,IAAA9B,SACtBH,cAACsC,IAAS,CAAAnC,SACPA,SAIJ,IAGLF,EAAKsC,UAAY,CACfpC,SAAUqC,IAAUC,KAAKC,WACzBxC,MAAOsC,IAAUG,OACjBX,KAAMQ,IAAUC,MAGHxC,K,mCC9Bf,aACA,MAAMb,EAASwD,cACAxD,K,kICAf,MAAM8C,EAAY,CAAC,YAAa,YAAa,iBAAkB,QAAS,WAAY,WAS9EW,EAAeC,cACfC,EAA+BC,YAAa,MAAO,CACvDC,KAAM,eACNC,KAAM,OACNC,kBAAmBA,CAACC,EAAOC,KACzB,MAAM,WACJC,GACEF,EACJ,MAAO,CAACC,EAAOE,KAAMF,EAAO,WAADG,OAAYC,YAAWC,OAAOJ,EAAW/D,aAAe+D,EAAWK,OAASN,EAAOM,MAAOL,EAAWM,gBAAkBP,EAAOO,eAAe,IAGtKC,EAAuBC,GAAWC,YAAoB,CAC1DX,MAAOU,EACPb,KAAM,eACNJ,iBAEImB,EAAoBA,CAACV,EAAYW,KACrC,MAGM,QACJC,EAAO,MACPP,EAAK,eACLC,EAAc,SACdrE,GACE+D,EACEa,EAAQ,CACZZ,KAAM,CAAC,OAAQhE,GAAY,WAAJiE,OAAeC,YAAWC,OAAOnE,KAAcoE,GAAS,QAASC,GAAkB,mBAE5G,OAAOQ,YAAeD,GAZWjB,GACxBmB,YAAqBJ,EAAef,IAWUgB,EAAQ,E,4BClCjE,MAAM5B,EDoCS,WAAuC,IAAdgC,EAAOC,UAAA7C,OAAA,QAAA8C,IAAAD,UAAA,GAAAA,UAAA,GAAG,CAAC,EACjD,MAAM,sBAEJE,EAAwB1B,EAA4B,cACpD2B,EAAgBb,EAAoB,cACpCI,EAAgB,gBACdK,EACEK,EAAgBF,GAAsBpF,IAAA,IAAC,MAC3CC,EAAK,WACLgE,GACDjE,EAAA,OAAKuF,YAAS,CACblE,MAAO,OACPmE,WAAY,OACZC,UAAW,aACXC,YAAa,OACbtF,QAAS,UACP6D,EAAWM,gBAAkB,CAC/BoB,YAAa1F,EAAM2F,QAAQ,GAC3BC,aAAc5F,EAAM2F,QAAQ,GAE5B,CAAC3F,EAAM6F,YAAYC,GAAG,OAAQ,CAC5BJ,YAAa1F,EAAM2F,QAAQ,GAC3BC,aAAc5F,EAAM2F,QAAQ,KAE9B,IAAEI,IAAA,IAAC,MACH/F,EAAK,WACLgE,GACD+B,EAAA,OAAK/B,EAAWK,OAASpC,OAAO+D,KAAKhG,EAAM6F,YAAYI,QAAQC,QAAO,CAACC,EAAKC,KAC3E,MAAMC,EAAaD,EACbE,EAAQtG,EAAM6F,YAAYI,OAAOI,GAOvC,OANc,IAAVC,IAEFH,EAAInG,EAAM6F,YAAYC,GAAGO,IAAe,CACtCpG,SAAU,GAAFiE,OAAKoC,GAAKpC,OAAGlE,EAAM6F,YAAYU,QAGpCJ,CAAG,GACT,CAAC,EAAE,IAAEK,IAAA,IAAC,MACPxG,EAAK,WACLgE,GACDwC,EAAA,OAAKlB,YAAS,CAAC,EAA2B,OAAxBtB,EAAW/D,UAAqB,CAEjD,CAACD,EAAM6F,YAAYC,GAAG,OAAQ,CAE5B7F,SAAUwG,KAAKC,IAAI1G,EAAM6F,YAAYI,OAAOU,GAAI,OAEjD3C,EAAW/D,UAEU,OAAxB+D,EAAW/D,UAAqB,CAE9B,CAACD,EAAM6F,YAAYC,GAAG9B,EAAW/D,WAAY,CAE3CA,SAAU,GAAFiE,OAAKlE,EAAM6F,YAAYI,OAAOjC,EAAW/D,WAASiE,OAAGlE,EAAM6F,YAAYU,QAEjF,IACIvD,EAAyB4D,cAAiB,SAAmBpC,EAAS/B,GAC1E,MAAMqB,EAAQsB,EAAcZ,IACtB,UACFqC,EAAS,UACTC,EAAY,MAAK,eACjBxC,GAAiB,EAAK,MACtBD,GAAQ,EAAK,SACbpE,EAAW,MACT6D,EACJnB,EAAQoE,YAA8BjD,EAAOlB,GACzCoB,EAAasB,YAAS,CAAC,EAAGxB,EAAO,CACrCgD,YACAxC,iBACAD,QACApE,aAII2E,EAAUF,EAAkBV,EAAYW,GAC9C,OAGEjE,aAHK,CAGA2E,EAAeC,YAAS,CAC3B0B,GAAIF,EAGJ9C,WAAYA,EACZ6C,UAAWI,YAAKrC,EAAQX,KAAM4C,GAC9BpE,IAAKA,GACJE,GAEP,IAWA,OAAOK,CACT,CCtIkBkE,CAAgB,CAChC/B,sBAAuBrF,YAAO,MAAO,CACnC6D,KAAM,eACNC,KAAM,OACNC,kBAAmBA,CAACC,EAAOC,KACzB,MAAM,WACJC,GACEF,EACJ,MAAO,CAACC,EAAOE,KAAMF,EAAO,WAADG,OAAYC,YAAWC,OAAOJ,EAAW/D,aAAe+D,EAAWK,OAASN,EAAOM,MAAOL,EAAWM,gBAAkBP,EAAOO,eAAe,IAG5Kc,cAAeZ,GAAWY,YAAc,CACtCtB,MAAOU,EACPb,KAAM,mBA8CKX,K,iIC/DR,SAASmE,EAA0BvD,GACxC,OAAOmB,YAAqB,gBAAiBnB,EAC/C,CAC0BwD,YAAuB,gBAAiB,CAAC,OAAQ,KAAM,KAAM,KAAM,KAAM,KAAM,KAAM,YAAa,YAAa,QAAS,QAAS,UAAW,SAAU,UAAW,WAAY,YAAa,aAAc,cAAe,eAAgB,SAAU,eAAgB,cAC5QC,I,OCJf,MAAMzE,EAAY,CAAC,QAAS,YAAa,YAAa,eAAgB,SAAU,YAAa,UAAW,kBAyB3F0E,EAAiBxH,YAAO,OAAQ,CAC3C6D,KAAM,gBACNC,KAAM,OACNC,kBAAmBA,CAACC,EAAOC,KACzB,MAAM,WACJC,GACEF,EACJ,MAAO,CAACC,EAAOE,KAAMD,EAAWhD,SAAW+C,EAAOC,EAAWhD,SAA+B,YAArBgD,EAAWuD,OAAuBxD,EAAO,QAADG,OAASC,YAAWH,EAAWuD,SAAWvD,EAAWwD,QAAUzD,EAAOyD,OAAQxD,EAAW/C,cAAgB8C,EAAO9C,aAAc+C,EAAWvC,WAAasC,EAAOtC,UAAU,GAP5P3B,EAS3BC,IAAA,IAAC,MACFC,EAAK,WACLgE,GACDjE,EAAA,OAAKuF,YAAS,CACbpF,OAAQ,GACP8D,EAAWhD,SAAWhB,EAAMyH,WAAWzD,EAAWhD,SAA+B,YAArBgD,EAAWuD,OAAuB,CAC/FrG,UAAW8C,EAAWuD,OACrBvD,EAAWwD,QAAU,CACtBE,SAAU,SACVC,aAAc,WACdC,WAAY,UACX5D,EAAW/C,cAAgB,CAC5B4G,aAAc,UACb7D,EAAWvC,WAAa,CACzBoG,aAAc,IACd,IACIC,EAAwB,CAC5BC,GAAI,KACJC,GAAI,KACJC,GAAI,KACJC,GAAI,KACJC,GAAI,KACJC,GAAI,KACJC,UAAW,KACXC,UAAW,KACXC,MAAO,IACPC,MAAO,IACPC,QAAS,KAILC,EAAuB,CAC3BC,QAAS,eACTC,YAAa,eACbC,UAAW,iBACXC,cAAe,iBACfC,MAAO,cAKHhI,EAA0B6F,cAAiB,SAAoBpC,EAAS/B,GAC5E,MAAMuG,EAAa5D,YAAc,CAC/BtB,MAAOU,EACPb,KAAM,kBAEFsF,EAR0BA,IACzBP,EAAqBO,IAAUA,EAOxBC,CAA0BF,EAAWC,OAC7CnF,EAAQqF,YAAa7D,YAAS,CAAC,EAAG0D,EAAY,CAClDC,YAEI,MACF1B,EAAQ,UAAS,UACjBV,EAAS,UACTC,EAAS,aACT7F,GAAe,EAAK,OACpBuG,GAAS,EAAK,UACd/F,GAAY,EAAK,QACjBT,EAAU,QAAO,eACjBoI,EAAiBtB,GACfhE,EACJnB,EAAQoE,YAA8BjD,EAAOlB,GACzCoB,EAAasB,YAAS,CAAC,EAAGxB,EAAO,CACrCyD,QACA0B,QACApC,YACAC,YACA7F,eACAuG,SACA/F,YACAT,UACAoI,mBAEIC,EAAYvC,IAAcrF,EAAY,IAAM2H,EAAepI,IAAY8G,EAAsB9G,KAAa,OAC1G4D,EAhGkBZ,KACxB,MAAM,MACJuD,EAAK,aACLtG,EAAY,OACZuG,EAAM,UACN/F,EAAS,QACTT,EAAO,QACP4D,GACEZ,EACEa,EAAQ,CACZZ,KAAM,CAAC,OAAQjD,EAA8B,YAArBgD,EAAWuD,OAAuB,QAAJrD,OAAYC,YAAWoD,IAAUtG,GAAgB,eAAgBuG,GAAU,SAAU/F,GAAa,cAE1J,OAAOqD,YAAeD,EAAOsC,EAA2BvC,EAAQ,EAoFhDF,CAAkBV,GAClC,OAAoBtD,cAAK4G,EAAgBhC,YAAS,CAChD0B,GAAIqC,EACJ5G,IAAKA,EACLuB,WAAYA,EACZ6C,UAAWI,YAAKrC,EAAQX,KAAM4C,IAC7BlE,GACL,IA4Ee5B,K", "file": "static/js/38.06c2fdc5.chunk.js", "sourcesContent": ["// @mui\nimport { styled } from \"@mui/material/styles\";\nimport { Box, Typography } from \"@mui/material\";\n\n// components\nimport Page from \"../../components/Page\";\n\n// Lazy load CarFront and ForgotPasswordForm components\nimport { lazy } from \"react\";\n\nconst CarFront = lazy(() => import(\"../../components/CarFront\"));\nconst ForgotPasswordForm = lazy(() => import(\"../../sections/auth/ForgotPasswordForm\"));\n\n// ----------------------------------------------------------------------\n\nconst ContentStyle = styled('div')(({ theme }) => ({\n  maxWidth: 480,\n  margin: 'auto',\n  display: 'flex',\n  minHeight: '100vh',\n  flexDirection: 'column',\n  justifyContent: 'center',\n  alignContent: 'space-between',\n  gap: 3\n}));\n\n// ----------------------------------------------------------------------\n\nexport default function ForgotPassword() {\n  return (\n    <Page title=\"Forgot Password\">\n      <ContentStyle>\n        <Typography variant=\"h3\" gutterBottom textAlign={'center'}>\n         Шинэ нууц үг үүсгэх\n         </Typography>\n\n        <Box width={\"50%\"} sx={{ mx: 'auto', mb: 3, mt: -3 }}>\n          <CarFront />\n        </Box>\n\n        {/* Updated Instruction Text */}\n        <Typography paragraph textAlign={'center'}>\n        Таны нууц үгээ сэргээхийн тулд төхөөрөмжийн дугаарын сүүлийн 6 оронг оруулна уу. Хэрэв та төхөөрөмжийн дугаарыг мэдэхгүй бол та машинруугаа мессежээр id гэж бичээд илгээж авна уу. Жич хариу авахад машинд нэгж хоног байхыг анхаарна уу.        </Typography>\n\n        <Box width={'80%'} mx={'auto'} my={3}>\n          <ForgotPasswordForm /> {/* Ensure this form accepts the new logic for resetting */}\n        </Box>\n      </ContentStyle>\n    </Page>\n  );\n}\n", "import objectWithoutPropertiesLoose from \"./objectWithoutPropertiesLoose.js\";\nfunction _objectWithoutProperties(e, t) {\n  if (null == e) return {};\n  var o,\n    r,\n    i = objectWithoutPropertiesLoose(e, t);\n  if (Object.getOwnPropertySymbols) {\n    var n = Object.getOwnPropertySymbols(e);\n    for (r = 0; r < n.length; r++) o = n[r], -1 === t.indexOf(o) && {}.propertyIsEnumerable.call(e, o) && (i[o] = e[o]);\n  }\n  return i;\n}\nexport { _objectWithoutProperties as default };", "import PropTypes from 'prop-types';\nimport { Helmet } from 'react-helmet-async';\nimport { forwardRef } from 'react';\n// @mui\nimport { Box, Container } from '@mui/material';\n\n// ----------------------------------------------------------------------\n\nconst Page = forwardRef(({ children, title = '', meta, ...other }, ref) => (\n  <>\n    <Helmet>\n      <title>{title}</title>\n      {meta}\n    </Helmet>\n\n    <Box ref={ref} {...other}>\n      <Container  >\n        {children}\n      </Container>\n\n    </Box>\n  </>\n));\n\nPage.propTypes = {\n  children: PropTypes.node.isRequired,\n  title: PropTypes.string,\n  meta: PropTypes.node,\n};\n\nexport default Page;\n", "import createStyled from './createStyled';\nconst styled = createStyled();\nexport default styled;", "import _objectWithoutPropertiesLoose from \"@babel/runtime/helpers/esm/objectWithoutPropertiesLoose\";\nimport _extends from \"@babel/runtime/helpers/esm/extends\";\nconst _excluded = [\"className\", \"component\", \"disableGutters\", \"fixed\", \"maxWidth\", \"classes\"];\nimport * as React from 'react';\nimport PropTypes from 'prop-types';\nimport clsx from 'clsx';\nimport { unstable_capitalize as capitalize, unstable_composeClasses as composeClasses, unstable_generateUtilityClass as generateUtilityClass } from '@mui/utils';\nimport useThemePropsSystem from '../useThemeProps';\nimport systemStyled from '../styled';\nimport createTheme from '../createTheme';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nconst defaultTheme = createTheme();\nconst defaultCreateStyledComponent = systemStyled('div', {\n  name: 'MuiContainer',\n  slot: 'Root',\n  overridesResolver: (props, styles) => {\n    const {\n      ownerState\n    } = props;\n    return [styles.root, styles[`maxWidth${capitalize(String(ownerState.maxWidth))}`], ownerState.fixed && styles.fixed, ownerState.disableGutters && styles.disableGutters];\n  }\n});\nconst useThemePropsDefault = inProps => useThemePropsSystem({\n  props: inProps,\n  name: 'MuiContainer',\n  defaultTheme\n});\nconst useUtilityClasses = (ownerState, componentName) => {\n  const getContainerUtilityClass = slot => {\n    return generateUtilityClass(componentName, slot);\n  };\n  const {\n    classes,\n    fixed,\n    disableGutters,\n    maxWidth\n  } = ownerState;\n  const slots = {\n    root: ['root', maxWidth && `maxWidth${capitalize(String(maxWidth))}`, fixed && 'fixed', disableGutters && 'disableGutters']\n  };\n  return composeClasses(slots, getContainerUtilityClass, classes);\n};\nexport default function createContainer(options = {}) {\n  const {\n    // This will allow adding custom styled fn (for example for custom sx style function)\n    createStyledComponent = defaultCreateStyledComponent,\n    useThemeProps = useThemePropsDefault,\n    componentName = 'MuiContainer'\n  } = options;\n  const ContainerRoot = createStyledComponent(({\n    theme,\n    ownerState\n  }) => _extends({\n    width: '100%',\n    marginLeft: 'auto',\n    boxSizing: 'border-box',\n    marginRight: 'auto',\n    display: 'block'\n  }, !ownerState.disableGutters && {\n    paddingLeft: theme.spacing(2),\n    paddingRight: theme.spacing(2),\n    // @ts-ignore module augmentation fails if custom breakpoints are used\n    [theme.breakpoints.up('sm')]: {\n      paddingLeft: theme.spacing(3),\n      paddingRight: theme.spacing(3)\n    }\n  }), ({\n    theme,\n    ownerState\n  }) => ownerState.fixed && Object.keys(theme.breakpoints.values).reduce((acc, breakpointValueKey) => {\n    const breakpoint = breakpointValueKey;\n    const value = theme.breakpoints.values[breakpoint];\n    if (value !== 0) {\n      // @ts-ignore\n      acc[theme.breakpoints.up(breakpoint)] = {\n        maxWidth: `${value}${theme.breakpoints.unit}`\n      };\n    }\n    return acc;\n  }, {}), ({\n    theme,\n    ownerState\n  }) => _extends({}, ownerState.maxWidth === 'xs' && {\n    // @ts-ignore module augmentation fails if custom breakpoints are used\n    [theme.breakpoints.up('xs')]: {\n      // @ts-ignore module augmentation fails if custom breakpoints are used\n      maxWidth: Math.max(theme.breakpoints.values.xs, 444)\n    }\n  }, ownerState.maxWidth &&\n  // @ts-ignore module augmentation fails if custom breakpoints are used\n  ownerState.maxWidth !== 'xs' && {\n    // @ts-ignore module augmentation fails if custom breakpoints are used\n    [theme.breakpoints.up(ownerState.maxWidth)]: {\n      // @ts-ignore module augmentation fails if custom breakpoints are used\n      maxWidth: `${theme.breakpoints.values[ownerState.maxWidth]}${theme.breakpoints.unit}`\n    }\n  }));\n  const Container = /*#__PURE__*/React.forwardRef(function Container(inProps, ref) {\n    const props = useThemeProps(inProps);\n    const {\n        className,\n        component = 'div',\n        disableGutters = false,\n        fixed = false,\n        maxWidth = 'lg'\n      } = props,\n      other = _objectWithoutPropertiesLoose(props, _excluded);\n    const ownerState = _extends({}, props, {\n      component,\n      disableGutters,\n      fixed,\n      maxWidth\n    });\n\n    // @ts-ignore module augmentation fails if custom breakpoints are used\n    const classes = useUtilityClasses(ownerState, componentName);\n    return (\n      /*#__PURE__*/\n      // @ts-ignore theme is injected by the styled util\n      _jsx(ContainerRoot, _extends({\n        as: component\n        // @ts-ignore module augmentation fails if custom breakpoints are used\n        ,\n        ownerState: ownerState,\n        className: clsx(classes.root, className),\n        ref: ref\n      }, other))\n    );\n  });\n  process.env.NODE_ENV !== \"production\" ? Container.propTypes /* remove-proptypes */ = {\n    children: PropTypes.node,\n    classes: PropTypes.object,\n    className: PropTypes.string,\n    component: PropTypes.elementType,\n    disableGutters: PropTypes.bool,\n    fixed: PropTypes.bool,\n    maxWidth: PropTypes /* @typescript-to-proptypes-ignore */.oneOfType([PropTypes.oneOf(['xs', 'sm', 'md', 'lg', 'xl', false]), PropTypes.string]),\n    sx: PropTypes.oneOfType([PropTypes.arrayOf(PropTypes.oneOfType([PropTypes.func, PropTypes.object, PropTypes.bool])), PropTypes.func, PropTypes.object])\n  } : void 0;\n  return Container;\n}", "/* eslint-disable material-ui/mui-name-matches-component-name */\nimport PropTypes from 'prop-types';\nimport { createContainer } from '@mui/system';\nimport capitalize from '../utils/capitalize';\nimport styled from '../styles/styled';\nimport useThemeProps from '../styles/useThemeProps';\nconst Container = createContainer({\n  createStyledComponent: styled('div', {\n    name: '<PERSON><PERSON><PERSON>ontaine<PERSON>',\n    slot: 'Root',\n    overridesResolver: (props, styles) => {\n      const {\n        ownerState\n      } = props;\n      return [styles.root, styles[`maxWidth${capitalize(String(ownerState.maxWidth))}`], ownerState.fixed && styles.fixed, ownerState.disableGutters && styles.disableGutters];\n    }\n  }),\n  useThemeProps: inProps => useThemeProps({\n    props: inProps,\n    name: 'MuiContainer'\n  })\n});\nprocess.env.NODE_ENV !== \"production\" ? Container.propTypes /* remove-proptypes */ = {\n  // ----------------------------- Warning --------------------------------\n  // | These PropTypes are generated from the TypeScript type definitions |\n  // |     To update them edit the d.ts file and run \"yarn proptypes\"     |\n  // ----------------------------------------------------------------------\n  /**\n   * @ignore\n   */\n  children: PropTypes.node,\n  /**\n   * Override or extend the styles applied to the component.\n   */\n  classes: PropTypes.object,\n  /**\n   * The component used for the root node.\n   * Either a string to use a HTML element or a component.\n   */\n  component: PropTypes.elementType,\n  /**\n   * If `true`, the left and right padding is removed.\n   * @default false\n   */\n  disableGutters: PropTypes.bool,\n  /**\n   * Set the max-width to match the min-width of the current breakpoint.\n   * This is useful if you'd prefer to design for a fixed set of sizes\n   * instead of trying to accommodate a fully fluid viewport.\n   * It's fluid by default.\n   * @default false\n   */\n  fixed: PropTypes.bool,\n  /**\n   * Determine the max-width of the container.\n   * The container width grows with the size of the screen.\n   * Set to `false` to disable `maxWidth`.\n   * @default 'lg'\n   */\n  maxWidth: PropTypes /* @typescript-to-proptypes-ignore */.oneOfType([PropTypes.oneOf(['xs', 'sm', 'md', 'lg', 'xl', false]), PropTypes.string]),\n  /**\n   * The system prop that allows defining system overrides as well as additional CSS styles.\n   */\n  sx: PropTypes.oneOfType([PropTypes.arrayOf(PropTypes.oneOfType([PropTypes.func, PropTypes.object, PropTypes.bool])), PropTypes.func, PropTypes.object])\n} : void 0;\nexport default Container;", "import { unstable_generateUtilityClasses as generateUtilityClasses } from '@mui/utils';\nimport generateUtilityClass from '../generateUtilityClass';\nexport function getTypographyUtilityClass(slot) {\n  return generateUtilityClass('MuiTypography', slot);\n}\nconst typographyClasses = generateUtilityClasses('MuiTypography', ['root', 'h1', 'h2', 'h3', 'h4', 'h5', 'h6', 'subtitle1', 'subtitle2', 'body1', 'body2', 'inherit', 'button', 'caption', 'overline', 'alignLeft', 'alignRight', 'alignCenter', 'alignJustify', 'noWrap', 'gutterBottom', 'paragraph']);\nexport default typographyClasses;", "import _objectWithoutPropertiesLoose from \"@babel/runtime/helpers/esm/objectWithoutPropertiesLoose\";\nimport _extends from \"@babel/runtime/helpers/esm/extends\";\nconst _excluded = [\"align\", \"className\", \"component\", \"gutterBottom\", \"noWrap\", \"paragraph\", \"variant\", \"variantMapping\"];\nimport * as React from 'react';\nimport PropTypes from 'prop-types';\nimport clsx from 'clsx';\nimport { unstable_extendSxProp as extendSxProp } from '@mui/system';\nimport { unstable_composeClasses as composeClasses } from '@mui/base';\nimport styled from '../styles/styled';\nimport useThemeProps from '../styles/useThemeProps';\nimport capitalize from '../utils/capitalize';\nimport { getTypographyUtilityClass } from './typographyClasses';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nconst useUtilityClasses = ownerState => {\n  const {\n    align,\n    gutterBottom,\n    noWrap,\n    paragraph,\n    variant,\n    classes\n  } = ownerState;\n  const slots = {\n    root: ['root', variant, ownerState.align !== 'inherit' && `align${capitalize(align)}`, gutterBottom && 'gutterBottom', noWrap && 'noWrap', paragraph && 'paragraph']\n  };\n  return composeClasses(slots, getTypographyUtilityClass, classes);\n};\nexport const TypographyRoot = styled('span', {\n  name: 'MuiTypography',\n  slot: 'Root',\n  overridesResolver: (props, styles) => {\n    const {\n      ownerState\n    } = props;\n    return [styles.root, ownerState.variant && styles[ownerState.variant], ownerState.align !== 'inherit' && styles[`align${capitalize(ownerState.align)}`], ownerState.noWrap && styles.noWrap, ownerState.gutterBottom && styles.gutterBottom, ownerState.paragraph && styles.paragraph];\n  }\n})(({\n  theme,\n  ownerState\n}) => _extends({\n  margin: 0\n}, ownerState.variant && theme.typography[ownerState.variant], ownerState.align !== 'inherit' && {\n  textAlign: ownerState.align\n}, ownerState.noWrap && {\n  overflow: 'hidden',\n  textOverflow: 'ellipsis',\n  whiteSpace: 'nowrap'\n}, ownerState.gutterBottom && {\n  marginBottom: '0.35em'\n}, ownerState.paragraph && {\n  marginBottom: 16\n}));\nconst defaultVariantMapping = {\n  h1: 'h1',\n  h2: 'h2',\n  h3: 'h3',\n  h4: 'h4',\n  h5: 'h5',\n  h6: 'h6',\n  subtitle1: 'h6',\n  subtitle2: 'h6',\n  body1: 'p',\n  body2: 'p',\n  inherit: 'p'\n};\n\n// TODO v6: deprecate these color values in v5.x and remove the transformation in v6\nconst colorTransformations = {\n  primary: 'primary.main',\n  textPrimary: 'text.primary',\n  secondary: 'secondary.main',\n  textSecondary: 'text.secondary',\n  error: 'error.main'\n};\nconst transformDeprecatedColors = color => {\n  return colorTransformations[color] || color;\n};\nconst Typography = /*#__PURE__*/React.forwardRef(function Typography(inProps, ref) {\n  const themeProps = useThemeProps({\n    props: inProps,\n    name: 'MuiTypography'\n  });\n  const color = transformDeprecatedColors(themeProps.color);\n  const props = extendSxProp(_extends({}, themeProps, {\n    color\n  }));\n  const {\n      align = 'inherit',\n      className,\n      component,\n      gutterBottom = false,\n      noWrap = false,\n      paragraph = false,\n      variant = 'body1',\n      variantMapping = defaultVariantMapping\n    } = props,\n    other = _objectWithoutPropertiesLoose(props, _excluded);\n  const ownerState = _extends({}, props, {\n    align,\n    color,\n    className,\n    component,\n    gutterBottom,\n    noWrap,\n    paragraph,\n    variant,\n    variantMapping\n  });\n  const Component = component || (paragraph ? 'p' : variantMapping[variant] || defaultVariantMapping[variant]) || 'span';\n  const classes = useUtilityClasses(ownerState);\n  return /*#__PURE__*/_jsx(TypographyRoot, _extends({\n    as: Component,\n    ref: ref,\n    ownerState: ownerState,\n    className: clsx(classes.root, className)\n  }, other));\n});\nprocess.env.NODE_ENV !== \"production\" ? Typography.propTypes /* remove-proptypes */ = {\n  // ----------------------------- Warning --------------------------------\n  // | These PropTypes are generated from the TypeScript type definitions |\n  // |     To update them edit the d.ts file and run \"yarn proptypes\"     |\n  // ----------------------------------------------------------------------\n  /**\n   * Set the text-align on the component.\n   * @default 'inherit'\n   */\n  align: PropTypes.oneOf(['center', 'inherit', 'justify', 'left', 'right']),\n  /**\n   * The content of the component.\n   */\n  children: PropTypes.node,\n  /**\n   * Override or extend the styles applied to the component.\n   */\n  classes: PropTypes.object,\n  /**\n   * @ignore\n   */\n  className: PropTypes.string,\n  /**\n   * The component used for the root node.\n   * Either a string to use a HTML element or a component.\n   */\n  component: PropTypes.elementType,\n  /**\n   * If `true`, the text will have a bottom margin.\n   * @default false\n   */\n  gutterBottom: PropTypes.bool,\n  /**\n   * If `true`, the text will not wrap, but instead will truncate with a text overflow ellipsis.\n   *\n   * Note that text overflow can only happen with block or inline-block level elements\n   * (the element needs to have a width in order to overflow).\n   * @default false\n   */\n  noWrap: PropTypes.bool,\n  /**\n   * If `true`, the element will be a paragraph element.\n   * @default false\n   */\n  paragraph: PropTypes.bool,\n  /**\n   * The system prop that allows defining system overrides as well as additional CSS styles.\n   */\n  sx: PropTypes.oneOfType([PropTypes.arrayOf(PropTypes.oneOfType([PropTypes.func, PropTypes.object, PropTypes.bool])), PropTypes.func, PropTypes.object]),\n  /**\n   * Applies the theme typography styles.\n   * @default 'body1'\n   */\n  variant: PropTypes /* @typescript-to-proptypes-ignore */.oneOfType([PropTypes.oneOf(['body1', 'body2', 'button', 'caption', 'h1', 'h2', 'h3', 'h4', 'h5', 'h6', 'inherit', 'overline', 'subtitle1', 'subtitle2']), PropTypes.string]),\n  /**\n   * The component maps the variant prop to a range of different HTML element types.\n   * For instance, subtitle1 to `<h6>`.\n   * If you wish to change that mapping, you can provide your own.\n   * Alternatively, you can use the `component` prop.\n   * @default {\n   *   h1: 'h1',\n   *   h2: 'h2',\n   *   h3: 'h3',\n   *   h4: 'h4',\n   *   h5: 'h5',\n   *   h6: 'h6',\n   *   subtitle1: 'h6',\n   *   subtitle2: 'h6',\n   *   body1: 'p',\n   *   body2: 'p',\n   *   inherit: 'p',\n   * }\n   */\n  variantMapping: PropTypes /* @typescript-to-proptypes-ignore */.object\n} : void 0;\nexport default Typography;"], "sourceRoot": ""}