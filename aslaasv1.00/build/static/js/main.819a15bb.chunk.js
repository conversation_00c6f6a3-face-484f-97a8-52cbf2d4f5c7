(this.webpackJsonpclient=this.webpackJsonpclient||[]).push([[6],{152:function(e,t,n){"use strict";n.d(t,"a",(function(){return c}));var i=n(121),a=n(178);function c(e,t,n,c){const r=Object(i.a)(),o=Object(a.a)(r.breakpoints.up(t)),s=Object(a.a)(r.breakpoints.down(t)),l=Object(a.a)(r.breakpoints.between(n,c)),d=Object(a.a)(r.breakpoints.only(t));return"up"===e?o:"down"===e?s:"between"===e?l:"only"===e?d:null}},172:function(e,t,n){"use strict";n.d(t,"a",(function(){return j})),n.d(t,"b",(function(){return b}));var i=n(8),a=n(0),c=n(37),r=n(327);n(185);const o=e=>{e?(localStorage.setItem("accessToken",e),c.a.defaults.headers.common.Authorization="Bearer ".concat(e)):(localStorage.removeItem("accessToken"),delete c.a.defaults.headers.common.Authorization)};var s=n(2);const l={isAuthenticated:!1,isInitialized:!1,user:null},d={INITIALIZE:(e,t)=>{const{isAuthenticated:n,user:a}=t.payload;return Object(i.a)(Object(i.a)({},e),{},{isAuthenticated:n,isInitialized:!0,user:a})},OTPFINAL:(e,t)=>{const{final:n}=t.payload;return Object(i.a)(Object(i.a)({},e),{},{isAuthenticated:!1,isInitialized:!0,final:n,user:null})},LOGINED:(e,t)=>{const{user:n}=t.payload;return Object(i.a)(Object(i.a)({},e),{},{isAuthenticated:!0,user:n})},LOGOUT:e=>Object(i.a)(Object(i.a)({},e),{},{isAuthenticated:!1,final:null,user:null})},u=(e,t)=>d[t.type]?d[t.type](e,t):e,j=Object(a.createContext)(Object(i.a)(Object(i.a)({},l),{},{method:"jwt",login:()=>Promise.resolve(),logout:()=>Promise.resolve(),initialize:()=>Promise.resolve()}));function b(e){let{children:t}=e;const[n,d]=Object(a.useReducer)(u,l),b=async()=>{try{const e=window.localStorage.getItem("accessToken");if(e&&(e=>{if(!e)return!1;const t=Object(r.a)(e),n=Date.now()/1e3;return t.exp>n})(e)){o(e);const t=await c.a.get("/api/auth/my-account"),{user:n}=t.data;d({type:"INITIALIZE",payload:{isAuthenticated:!0,user:n}})}else d({type:"INITIALIZE",payload:{isAuthenticated:!1,user:null}})}catch(e){console.error(e),d({type:"INITIALIZE",payload:{isAuthenticated:!1,user:null}})}};Object(a.useEffect)((()=>{console.log("--------------iniitalize passport-------------------"),b()}),[]);return Object(s.jsxs)(j.Provider,{value:Object(i.a)(Object(i.a)({},n),{},{method:"jwt",login:async e=>{const t=await c.a.post("/api/auth/login",{phoneNumber:e});if(t.data.pinVerify)return"pincode";const{token:n,user:i}=t.data;if(n)return"inactive"===i.status?"inactive":(o(n),d({type:"LOGINED",payload:{user:i}}),"navigate");try{return window.phoneNumber=e,"otp"}catch(a){return console.log(a),"otp"}},logout:async()=>{try{o(null),d({type:"LOGOUT"})}catch(e){console.log(e)}},otpVerify:async(e,t)=>{try{const n=window.phoneNumber,i=await c.a.post("/api/auth/verifyOtp",{phoneNumber:n,otp:e}),{success:a}=i.data;if(a){const e=await c.a.post("/api/auth/register",{phoneNumber:n});if(200===e.status){const{token:n,user:i}=e.data;o(n),d({type:"LOGINED",payload:{user:i}}),t({success:!0})}}else t({success:!1,err:"unmathed otpcode"})}catch(n){t({success:!1,err:"otp response err"})}},codeVerify:async(e,t)=>{try{const n=await c.a.post("/api/auth/pincode",{phoneNumber:e,pinCode:t});if(n.data.requiresTwoFactor)return{success:n.data.success,requiresTwoFactor:!0,message:n.data.message,phoneNumber:n.data.phoneNumber};const{token:i,user:r}=n.data;if(!i)try{return window.phoneNumber=e,{success:!1,message:"pin code verification error"}}catch(a){return{success:!1,message:"pin code verification error"}}return"inactive"===r.status?{success:!1,message:"Your account is inactive. Please contact with administrator"}:(o(i),d({type:"LOGINED",payload:{user:r}}),{success:!0,message:"verification successfully"})}catch(r){var n,i;return console.error("Pin code verification error:",r),{success:!1,message:(null===(n=r.response)||void 0===n||null===(i=n.data)||void 0===i?void 0:i.message)||"Pin code verification error"}}},verify2FA:async function(e,t){let n=arguments.length>2&&void 0!==arguments[2]&&arguments[2];try{const i=await c.a.post("/api/2fa/verify",{phoneNumber:e,token:t,isBackupCode:n});if(console.log("2FA verify response:",i.data),200===i.data.status){const t=await c.a.post("/api/auth/complete-login-2fa",{phoneNumber:e});if(console.log("Complete login response:",t.data),t.data.success){const{token:e,user:n}=t.data;return o(e),d({type:"LOGINED",payload:{user:n}}),{success:!0}}}return{success:!1,message:i.data.message||"Verification failed"}}catch(r){var i,a;return console.error("2FA verification error:",r),{success:!1,message:(null===(i=r.response)||void 0===i||null===(a=i.data)||void 0===a?void 0:a.message)||"Verification failed"}}},initialize:b}),children:[" ",t," "]})}},233:function(e,t,n){"use strict";n.d(t,"b",(function(){return i})),n.d(t,"a",(function(){return a})),n.d(t,"c",(function(){return c}));const i="https://www.aslaa.mn/",a={MOBILE_HEIGHT:50,MAIN_DESKTOP_HEIGHT:70};n(242);const c=5e3},237:function(e,t,n){"use strict";n.d(t,"a",(function(){return o}));var i=n(8),a=n(53),c=n(521),r=n(2);function o(e){let{disabledLink:t=!1,sx:n}=e;const o=Object(r.jsx)(c.a,{sx:Object(i.a)({width:60,height:40},n),children:Object(r.jsx)("img",{width:"100%",height:"100%",src:"/logo/logo.png",alt:"logo"})});return t?Object(r.jsxs)(r.Fragment,{children:[" ",o," "]}):Object(r.jsxs)(a.b,{to:"/home",children:[" ",o," "]})}},241:function(e,t,n){"use strict";n.d(t,"b",(function(){return s}));var i=n(105),a=n(37),c=n(87);const r=Object(i.b)({name:"notification",initialState:{isLoading:!1,error:null,notifications:[]},reducers:{startLoading(e){e.isLoading=!0},hasError(e,t){e.error=t.payload,e.isLoading=!1},setNotifications(e,t){e.isLoading=!1,e.notifications=t.payload}}});t.a=r.reducer;const{setNotifications:o}=r.actions;function s(){return async()=>{Object(c.a)(r.actions.startLoading());try{const e=await a.a.get("/api/log/sim-status");Object(c.a)(r.actions.setNotifications(e.data.data))}catch(e){Object(c.a)(r.actions.hasError(e))}}}},242:function(e,t,n){"use strict";n.d(t,"b",(function(){return r}));var i=n(326),a=n(214);n.d(t,"a",(function(){return a.b}));const c=Object(i.a)({apiKey:"AIzaSyDA_x4YElOAVmT4rS3B-xcmCzhvefDTOrI",authDomain:"rccdemo-41279.firebaseapp.com",projectId:"rccdemo-41279",storageBucket:"rccdemo-41279.appspot.com",messagingSenderId:"963013875719",appId:"1:963013875719:web:f9511f343bceb59b06f2a2"}),r=Object(a.a)(c)},314:function(e,t,n){"use strict";n.d(t,"a",(function(){return s}));var i=n(5),a=n(48),c=n(237),r=n(2);const o=Object(a.a)("header")((e=>{let{theme:t}=e;return{top:0,left:0,lineHeight:0,maxWidth:"600px",position:"absolute",padding:t.spacing(3,3,0),[t.breakpoints.up("sm")]:{padding:t.spacing(5,5,0)}}}));function s(){return Object(r.jsxs)(r.Fragment,{children:[Object(r.jsx)(o,{children:Object(r.jsx)(c.a,{disabledLink:!0})}),Object(r.jsx)(i.b,{})]})}},351:function(e,t,n){},361:function(e,t,n){},362:function(e,t,n){},37:function(e,t,n){"use strict";var i=n(325),a=n.n(i),c=n(233);const r=a.a.create({baseURL:c.b});r.interceptors.response.use((e=>e),(e=>Promise.reject(e.response&&e.response.data||"Something went wrong"))),t.a=r},387:function(e,t){},389:function(e,t){},401:function(e,t){},403:function(e,t){},431:function(e,t){},433:function(e,t){},434:function(e,t){},439:function(e,t){},441:function(e,t){},447:function(e,t){},449:function(e,t){},468:function(e,t){},480:function(e,t){},483:function(e,t){},508:function(e,t,n){"use strict";n.r(t);n(347),n(348),n(349),n(350),n(351);var i=n(50),a=n.n(i),c=n(53),r=n(232),o=n(130),s=n(323);var l=e=>{e&&e instanceof Function&&n.e(45).then(n.bind(null,1285)).then((t=>{let{getCLS:n,getFID:i,getFCP:a,getLCP:c,getTTFB:r}=t;n(e),i(e),a(e),c(e),r(e)}))},d=(n(361),n(362),n(87)),u=n(172),j=n(8),b=n(0),h=n(152),f=n(2);const O={collapseClick:!1,collapseHover:!1,onToggleCollapse:()=>{},onHoverEnter:()=>{},onHoverLeave:()=>{}},m=Object(b.createContext)(O);function p(e){let{children:t}=e;const n=Object(h.a)("up","lg"),[i,a]=Object(b.useState)({click:!1,hover:!1});Object(b.useEffect)((()=>{n||a({click:!1,hover:!1})}),[n]);return Object(f.jsx)(m.Provider,{value:{isCollapse:i.click&&!i.hover,collapseClick:i.click,collapseHover:i.hover,onToggleCollapse:()=>{a(Object(j.a)(Object(j.a)({},i),{},{click:!i.click}))},onHoverEnter:()=>{i.click&&a(Object(j.a)(Object(j.a)({},i),{},{hover:!0}))},onHoverLeave:()=>{a(Object(j.a)(Object(j.a)({},i),{},{hover:!1}))}},children:t})}var g=n(547),x=n(540),y=n(337),v=n(501),w=n(546),k=n(539);function z(e,t){return"linear-gradient(to bottom, ".concat(e,", ").concat(t,")")}const F={lighter:"#2ee7ff",light:"#38e8ff",main:"#33848f",dark:"#01060a",darker:"#00060a"},S={lighter:"#D0F2FF",light:"#74CAFF",main:"#1890FF",dark:"#0C53B7",darker:"#04297A"},P={lighter:"#E9FCD4",light:"#AAF27F",main:"#54D62C",dark:"#229A16",darker:"#08660D"},A={lighter:"#FFF7CD",light:"#FFE16A",main:"#FFC107",dark:"#B78103",darker:"#7A4F01"},I={lighter:"#FFE7D9",light:"#FFA48D",main:"#FF4842",dark:"#B72136",darker:"#7A0C2E"},L={0:"#FFFFFF",100:"#38e8ff",200:"#38B1FF",300:"#D0F2FF",400:"#004F99",500:"#38e8ff",600:"#061C2A",700:"#0a1217",800:"#040e16",900:"#00060a",5008:Object(k.a)("#38e8ff",.08),50012:Object(k.a)("#38e8ff",.12),50016:Object(k.a)("#38e8ff",.16),50024:Object(k.a)("#38e8ff",.24),50032:Object(k.a)("#38e8ff",.32),50048:Object(k.a)("#38e8ff",.48),50056:Object(k.a)("#38e8ff",.56),50080:Object(k.a)("#38e8ff",.8)},E={primary:z(F.light,F.main),info:z(S.light,S.main),success:z(P.light,P.main),warning:z(A.light,A.main),error:z(I.light,I.main)},T={common:{black:"#000",white:"#f0f0f0"},primary:Object(j.a)(Object(j.a)({},F),{},{contrastText:"#f0f0f0"}),secondary:Object(j.a)(Object(j.a)({},{lighter:"#D6E4FF",light:"#84A9FF",main:"#3366FF",dark:"#1939B7",darker:"#091A7A"}),{},{contrastText:"#f0f0f0"}),info:Object(j.a)(Object(j.a)({},S),{},{contrastText:"#f0f0f0"}),success:Object(j.a)(Object(j.a)({},P),{},{contrastText:L[800]}),warning:Object(j.a)(Object(j.a)({},A),{},{contrastText:L[800]}),error:Object(j.a)(Object(j.a)({},I),{},{contrastText:"#f0f0f0"}),grey:L,gradients:E,divider:L[50024],action:{hover:L[700],selected:L[700],disabled:L[50080],disabledBackground:L[50024],focus:L[700],hoverOpacity:.8,disabledOpacity:.48}};var C={dark:Object(j.a)(Object(j.a)({},T),{},{mode:"dark",text:{primary:"#f0f0f0",secondary:"#f0f0f0",disabled:"#a3a3a3"},background:{paper:"#000000",default:"#000000",neutral:"#262626"},action:Object(j.a)({active:"#737373"},T.action)})};n(121);function H(e){return"".concat(e/16,"rem")}function N(e){let{sm:t,md:n,lg:i}=e;return{"@media (min-width:600px)":{fontSize:H(t)},"@media (min-width:900px)":{fontSize:H(n)},"@media (min-width:1200px)":{fontSize:H(i)}}}const W="Orbitron, monospace",D="Roboto Mono, monospace";var B={fontFamily:"Public Sans, sans-serif",fontWeightRegular:400,fontWeightMedium:600,fontWeightBold:700,h1:Object(j.a)({fontWeight:700,lineHeight:1.25,fontSize:H(40),letterSpacing:2},N({sm:52,md:58,lg:64})),h2:Object(j.a)({fontWeight:700,lineHeight:64/48,fontSize:H(32)},N({sm:40,md:44,lg:48})),h3:Object(j.a)({fontWeight:700,lineHeight:1.5,fontSize:H(24)},N({sm:26,md:30,lg:32})),h4:Object(j.a)({fontWeight:700,lineHeight:1.5,fontSize:H(20)},N({sm:20,md:24,lg:24})),h5:Object(j.a)({fontWeight:700,lineHeight:1.5,fontSize:H(18)},N({sm:19,md:20,lg:20})),h6:Object(j.a)({fontWeight:700,lineHeight:28/18,fontSize:H(17)},N({sm:18,md:18,lg:18})),subtitle1:{fontWeight:600,lineHeight:1.5,fontSize:H(16)},subtitle2:{fontWeight:600,lineHeight:22/14,fontSize:H(14)},body1:{lineHeight:1.5,fontSize:H(16)},body2:{lineHeight:22/14,fontSize:H(14)},caption:{lineHeight:1.5,fontSize:H(12)},overline:{fontWeight:700,lineHeight:1.5,fontSize:H(12),textTransform:"uppercase"},button:{fontWeight:700,lineHeight:24/14,fontSize:H(14),textTransform:"capitalize"},digitalDisplay:{fontFamily:W,fontWeight:600,fontSize:H(16),letterSpacing:"0.1em",lineHeight:1.4},automotiveData:{fontFamily:D,fontWeight:500,fontSize:H(14),letterSpacing:"0.05em",lineHeight:1.3},automotiveLabel:{fontFamily:D,fontWeight:400,fontSize:H(14),letterSpacing:"0.05em",lineHeight:1.3},technicalInfo:{fontFamily:D,fontWeight:400,fontSize:H(12),letterSpacing:"0.05em",lineHeight:1.2},carStatus:{fontFamily:W,fontWeight:700,fontSize:H(18),letterSpacing:"0.15em",lineHeight:1.5}};var G={values:{xs:350,sm:600,md:900,lg:1200,xl:1536}};function R(e){let{children:t}=e;const n=Object(b.useMemo)((()=>({palette:C.dark,typography:B,breakpoints:G,shape:{borderRadius:8}})),[]),i=Object(y.a)(n);return Object(f.jsx)(v.a,{injectFirst:!0,children:Object(f.jsxs)(w.a,{theme:i,children:[Object(f.jsx)(x.a,{}),t]})})}var V=n(230);var q=e=>{let{children:t}=e;return Object(f.jsx)(V.a,{maxSnack:3,children:t})},U=n(544);var M=e=>{let{children:t}=e;return Object(f.jsx)(U.a.div,{initial:{opacity:0},animate:{opacity:1},exit:{opacity:0},children:t})},Z=n(5),_=n(97);function J(e){let{children:t}=e;const{isAuthenticated:n}=Object(_.a)();return n?Object(f.jsx)(Z.a,{to:"/home"}):Object(f.jsx)(f.Fragment,{children:t})}const K=Object(b.lazy)((()=>Promise.all([n.e(0),n.e(1),n.e(2),n.e(3),n.e(15)]).then(n.bind(null,1318)))),Y=Object(b.lazy)((()=>Promise.all([n.e(0),n.e(2),n.e(5),n.e(19),n.e(39)]).then(n.bind(null,1329)))),$=Object(b.lazy)((()=>n.e(38).then(n.bind(null,1293))));var Q={path:"auth",children:[{path:"login",element:Object(f.jsx)(J,{children:Object(f.jsx)(K,{})})},{path:"verify",element:Object(f.jsxs)(J,{children:[" ",Object(f.jsx)(Y,{})]})},{path:"forgot-password",element:Object(f.jsxs)(J,{children:[" ",Object(f.jsx)($,{})]})}]},X=n(548);var ee=()=>Object(f.jsx)("div",{style:{display:"flex",justifyContent:"center",alignItems:"center",height:"100vh"},children:Object(f.jsx)(X.a,{})});function te(e){let{children:t}=e;const{isAuthenticated:n,isInitialized:i}=Object(_.a)(),{pathname:a}=Object(Z.j)(),[c,r]=Object(b.useState)(null);return i?n?c&&a!==c?(r(null),Object(f.jsx)(Z.a,{to:c})):Object(f.jsx)(f.Fragment,{children:t}):(a!==c&&r(a),Object(f.jsx)(Z.a,{to:"/auth/login"})):Object(f.jsx)(ee,{})}const ne=Object(b.lazy)((()=>Promise.all([n.e(0),n.e(1),n.e(2),n.e(3),n.e(20)]).then(n.bind(null,1310)))),ie=Object(b.lazy)((()=>Promise.all([n.e(0),n.e(1),n.e(4),n.e(5),n.e(36)]).then(n.bind(null,1314)))),ae=Object(b.lazy)((()=>n.e(43).then(n.t.bind(null,1294,7)))),ce=Object(b.lazy)((()=>Promise.all([n.e(0),n.e(1),n.e(2),n.e(3),n.e(22)]).then(n.bind(null,1316)))),re=Object(b.lazy)((()=>Promise.all([n.e(0),n.e(1),n.e(4),n.e(33),n.e(40)]).then(n.bind(null,1295)))),oe=Object(b.lazy)((()=>Promise.all([n.e(0),n.e(1),n.e(2),n.e(3),n.e(16)]).then(n.bind(null,1296)))),se=Object(b.lazy)((()=>Promise.all([n.e(0),n.e(1),n.e(2),n.e(3),n.e(11)]).then(n.bind(null,1330)))),le=Object(b.lazy)((()=>Promise.all([n.e(0),n.e(1),n.e(2),n.e(3),n.e(17)]).then(n.bind(null,1312)))),de=Object(b.lazy)((()=>Promise.all([n.e(0),n.e(1),n.e(2),n.e(3),n.e(34)]).then(n.bind(null,1297)))),ue=Object(b.lazy)((()=>Promise.all([n.e(0),n.e(1),n.e(2),n.e(3),n.e(31)]).then(n.bind(null,1298)))),je=Object(b.lazy)((()=>Promise.all([n.e(0),n.e(1),n.e(2),n.e(3),n.e(26)]).then(n.bind(null,1320)))),be=Object(b.lazy)((()=>Promise.all([n.e(0),n.e(1),n.e(2),n.e(3),n.e(27)]).then(n.bind(null,1299)))),he=Object(b.lazy)((()=>Promise.all([n.e(0),n.e(1),n.e(2),n.e(3),n.e(29)]).then(n.bind(null,1300)))),fe=Object(b.lazy)((()=>Promise.all([n.e(0),n.e(1),n.e(2),n.e(3),n.e(25)]).then(n.bind(null,1301)))),Oe=Object(b.lazy)((()=>Promise.all([n.e(0),n.e(1),n.e(2),n.e(3),n.e(9)]).then(n.bind(null,1302)))),me=Object(b.lazy)((()=>Promise.all([n.e(0),n.e(1),n.e(2),n.e(3),n.e(30)]).then(n.bind(null,1303)))),pe=Object(b.lazy)((()=>Promise.all([n.e(0),n.e(1),n.e(2),n.e(3),n.e(21)]).then(n.bind(null,1304)))),ge=Object(b.lazy)((()=>Promise.all([n.e(0),n.e(1),n.e(2),n.e(3),n.e(23)]).then(n.bind(null,1305)))),xe=Object(b.lazy)((()=>Promise.all([n.e(0),n.e(1),n.e(2),n.e(3),n.e(32)]).then(n.bind(null,1317)))),ye=Object(b.lazy)((()=>Promise.all([n.e(0),n.e(1),n.e(2),n.e(3),n.e(35)]).then(n.bind(null,1306))));var ve={path:"",children:[{element:Object(f.jsx)(ie,{}),index:!0},{path:"home",element:Object(f.jsxs)(te,{children:[" ",Object(f.jsx)(ne,{})," "]})},{path:"help",element:Object(f.jsx)(re,{})},{path:"device-register",element:Object(f.jsxs)(te,{children:[" ",Object(f.jsx)(Oe,{})," "]})},{path:"license-profile",element:Object(f.jsxs)(te,{children:[" ",Object(f.jsx)(he,{})," "]})},{path:"pin-code",element:Object(f.jsxs)(te,{children:[" ",Object(f.jsx)(ae,{})," "]})},{path:"time-command",element:Object(f.jsxs)(te,{children:[" ",Object(f.jsx)(ce,{})," "]})},{path:"log-management",element:Object(f.jsxs)(te,{children:[" ",Object(f.jsx)(oe,{})," "]})},{path:"log-map",element:Object(f.jsxs)(te,{children:[" ",Object(f.jsx)(de,{})," "]})},{path:"log-sim",element:Object(f.jsxs)(te,{children:[" ",Object(f.jsx)(ue,{})," "]})},{path:"log-gps",element:Object(f.jsxs)(te,{children:[" ",Object(f.jsx)(je,{})," "]})},{path:"configure-driver",element:Object(f.jsxs)(te,{children:[" ",Object(f.jsx)(be,{})," "]})},{path:"configure-gps/:id",element:Object(f.jsxs)(te,{children:[" ",Object(f.jsx)(me,{})," "]})},{path:"log-license",element:Object(f.jsxs)(te,{children:[" ",Object(f.jsx)(se,{})," "]})},{path:"Order",element:Object(f.jsxs)(te,{children:[" ",Object(f.jsx)(pe,{})," "]})},{path:"driver-profile",element:Object(f.jsxs)(te,{children:[" ",Object(f.jsx)(fe,{})," "]})},{path:"notification",element:Object(f.jsxs)(te,{children:[" ",Object(f.jsx)(le,{})," "]})},{path:"device-config/:id",element:Object(f.jsx)(ge,{})},{path:"mqtt-test",element:Object(f.jsx)(xe,{})},{path:"mqtt-tcp-test",element:Object(f.jsx)(xe,{})},{path:"paho-mqtt",element:Object(f.jsx)(ye,{})}]};function we(e){var t;let{children:n}=e;const{isAuthenticated:i,isInitialized:a,user:c}=Object(_.a)();return a?i?null!==c&&void 0!==c&&null!==(t=c.role)&&void 0!==t&&t.includes("admin")?Object(f.jsx)(f.Fragment,{children:n}):Object(f.jsx)(Z.a,{to:"/"}):Object(f.jsx)(Z.a,{to:"/auth/login"}):Object(f.jsx)(ee,{})}const ke=Object(b.lazy)((()=>Promise.all([n.e(0),n.e(1),n.e(2),n.e(3),n.e(10)]).then(n.bind(null,1321)))),ze=Object(b.lazy)((()=>Promise.all([n.e(0),n.e(1),n.e(2),n.e(3),n.e(24)]).then(n.bind(null,1315)))),Fe=Object(b.lazy)((()=>Promise.all([n.e(0),n.e(1),n.e(2),n.e(3),n.e(12)]).then(n.bind(null,1307)))),Se=Object(b.lazy)((()=>Promise.all([n.e(0),n.e(1),n.e(2),n.e(3),n.e(13)]).then(n.bind(null,1308)))),Pe=Object(b.lazy)((()=>Promise.all([n.e(0),n.e(1),n.e(2),n.e(3),n.e(14)]).then(n.bind(null,1331)))),Ae=Object(b.lazy)((()=>Promise.all([n.e(0),n.e(1),n.e(2),n.e(3),n.e(28)]).then(n.bind(null,1322))));var Ie={path:"admin",children:[{path:"device/:id",element:Object(f.jsx)(te,{children:Object(f.jsx)(we,{children:Object(f.jsx)(ke,{})})})},{path:"user-manage",element:Object(f.jsx)(te,{children:Object(f.jsx)(we,{children:Object(f.jsx)(ze,{})})})},{path:"orders",element:Object(f.jsx)(te,{children:Object(f.jsx)(we,{children:Object(f.jsx)(Pe,{})})})},{path:"transactions",element:Object(f.jsx)(te,{children:Object(f.jsx)(we,{children:Object(f.jsx)(Fe,{})})})},{path:"withdraw-requests",element:Object(f.jsx)(te,{children:Object(f.jsx)(we,{children:Object(f.jsx)(Se,{})})})},{path:"app-management",element:Object(f.jsx)(te,{children:Object(f.jsx)(we,{children:Object(f.jsx)(Ae,{})})})}]},Le=n(314);const Ee=Object(b.lazy)((()=>Promise.all([n.e(0),n.e(1),n.e(4),n.e(37)]).then(n.bind(null,1311))));var Te={path:"*",element:Object(f.jsx)(Le.a,{}),children:[{path:"404",element:Object(f.jsx)(Ee,{})},{path:"*",element:Object(f.jsx)(Z.a,{to:"/404",replace:!0})}]};function Ce(){return Object(Z.p)([Q,ve,Ie,Te])}var He=function(){return Object(f.jsx)("footer",{style:{display:"flex",justifyContent:"center",alignItems:"center",padding:"10px"},children:Object(f.jsx)("p",{style:{margin:"0 10px"},children:"\xa9 2025 Elec.mn All Rights Reserved."})})};function Ne(){const{i18n:e}=Object(g.a)(),t=localStorage.getItem("language")||"en";return Object(b.useEffect)((()=>{e.changeLanguage(t)}),[t,e]),Object(f.jsxs)(R,{children:[Object(f.jsx)(q,{children:Object(f.jsx)(M,{children:Object(f.jsx)(b.Suspense,{fallback:Object(f.jsx)(ee,{}),children:Object(f.jsx)(Ce,{})})})}),Object(f.jsx)(He,{})]})}const We=Boolean("localhost"===window.location.hostname||"[::1]"===window.location.hostname||window.location.hostname.match(/^127(?:\.(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)){3}$/));function De(e,t){navigator.serviceWorker.register(e).then((e=>{e.onupdatefound=()=>{const n=e.installing;null!=n&&(n.onstatechange=()=>{"installed"===n.state&&(navigator.serviceWorker.controller?(console.log("New content is available and will be used when all tabs for this page are closed. See https://cra.link/PWA."),t&&t.onUpdate&&t.onUpdate(e)):(console.log("Content is cached for offline use."),t&&t.onSuccess&&t.onSuccess(e)))})}})).catch((e=>{console.error("Error during service worker registration:",e)}))}var Be=n(210),Ge=n(334),Re=n(336),Ve=n(140);Be.a.use(Ge.a).use(Re.a).use(Ve.e).init({debug:!1,fallbackLng:"en",lng:"mn",whitelist:["mn","en","ru"],interpolation:{escapeValue:!1},react:{useSuspense:!1}});Be.a;a.a.render(Object(f.jsx)(u.b,{children:Object(f.jsx)(r.b,{children:Object(f.jsx)(o.a,{store:d.c,children:Object(f.jsx)(s.PersistGate,{loading:null,persistor:d.b,children:Object(f.jsx)(p,{children:Object(f.jsx)(c.a,{children:Object(f.jsx)(Ne,{})})})})})})}),document.getElementById("root")),function(e){if("serviceWorker"in navigator){if(new URL("",window.location.href).origin!==window.location.origin)return;window.addEventListener("load",(()=>{const t="".concat("","/service-worker.js");We?(!function(e,t){fetch(e,{headers:{"Service-Worker":"script"}}).then((n=>{const i=n.headers.get("content-type");404===n.status||null!=i&&-1===i.indexOf("javascript")?navigator.serviceWorker.ready.then((e=>{e.unregister().then((()=>{window.location.reload()}))})):De(e,t)})).catch((()=>{console.log("No internet connection found. App is running in offline mode.")}))}(t,e),navigator.serviceWorker.ready.then((()=>{console.log("This web app is being served cache-first by a service worker. To learn more, visit https://cra.link/PWA")}))):De(t,e)}))}}(),l()},87:function(e,t,n){"use strict";n.d(t,"c",(function(){return O})),n.d(t,"b",(function(){return m})),n.d(t,"a",(function(){return p}));var i=n(105),a=n(130),c=n(224),r=n(29),o=n(324),s=n.n(o);const l=Object(i.b)({name:"car",initialState:{isLoading:!1,error:null,status:null,routines:null},reducers:{startLoading(e){e.isLoading=!0},hasError(e,t){e.error=t.payload,e.isLoading=!1},setCarStatus(e,t){e.isLoading=!1,e.status=t.payload},setRoutines(e,t){e.routines=t.payload}}});var d=l.reducer;const{setCarStatus:u,setRoutines:j}=l.actions;var b=n(241);const h={key:"root",storage:s.a,keyPrefix:"redux-",whitelist:["car"]},f=Object(r.b)({car:d,notification:b.a}),O=Object(i.a)({reducer:Object(c.a)(h,f),middleware:e=>e({serializableCheck:!1,immutableCheck:!1})}),m=Object(c.b)(O),{dispatch:p}=O;a.c},97:function(e,t,n){"use strict";var i=n(0),a=n(172);t.a=()=>{const e=Object(i.useContext)(a.a);if(!e)throw new Error("Auth context must be use inside AuthProvider");return e}}},[[508,7,8]]]);
//# sourceMappingURL=main.819a15bb.chunk.js.map