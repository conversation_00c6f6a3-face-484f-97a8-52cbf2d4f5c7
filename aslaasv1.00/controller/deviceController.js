const Device = require("../models/device");
const Driver = require("../models/driver");

const {
  sendMqtt,
  sendSms,
  checkMqttClient,
  getMqttClients,
} = require("../utils/channel");
const ADMIN_PHONE_NUMBER = process.env.ADMIN_PHONE_NUMBER;
const User = require("../models/user");
const LogModel = require("../models/log");
const DeviceMessageLogModel = require("../models/deviceMessageLog");
const MopedMessageLogModel = require("../models/mopedMessageLog");

const SmsLogModel = require("../models/smsMessageLog");
const {
  unsubscribe,
  subscribe,
  publish,
  subscribeCar2,
  unsubscribeCar2,
} = require("../utils/mqtt");
const Order = require("../models/order");
const { getBankList } = require("../utils/QPayment");
const DriverProfile = require("../models/driverProfile");
const carRentLog = require("../models/carRentLog");
//moped rentlog
const mopedRentLog = require("../models/mopedRentLog");
const ObjectId = require("mongoose").Types.ObjectId;
const {
  getCarRentPrice,
  getCarRentPriceTable,
  getRentableCarList,
  getMopedRentPrice,
  getMopedRentPriceTable,
  getRentableMopedList
} = require("../utils/rentService");
const WalletModel = require("../models/wallet");
const numeral = require("numeral");

let cmds = [];
cmds[":turnon"] = "as";
cmds[":turnoff"] = "untar";
cmds[":lock"] = "lock";
cmds[":unlock"] = "unlock";
cmds[":check"] = "check"; //this command is retrieve from data from gps
cmds[":on1"] = "on1";
cmds[":on2"] = "on2";
cmds[":off1"] = "off1";
cmds[":off2"] = "off2";
cmds[":temp"] = "temp";
let smsCmds = [];
smsCmds[":turnoff"] = "untar";
smsCmds[":turnon"] = "as";
smsCmds[":on1"] = "ON1";
smsCmds[":on2"] = "ON2";
smsCmds[":off1"] = "OFF1";
smsCmds[":off2"] = "OFF2";
smsCmds[":check"] = "check";

const driverConfirm = async (req, res) => {
  try {
    const { phoneNumber, drivername, address } = req.body;
    let driverProfile = await DriverProfile.findOne({ phoneNumber });
    if (driverProfile == null) {
      driverProfile = new DriverProfile({
        phoneNumber,
        drivername,
        address,
      });
      // console.log(driverProfile)
      await driverProfile.save();
      // const qpay = await getBankList(req, res, req.user.$set_id);

      //  if (response != null) {

      res.json({ success: true, message: "Order sucess" });
    } else {
      if (!DriverProfile.paid) {
        // const qpay = await getBankList(req, res, req.user._id);
        return res.json({
          success: false,
          message: `Already submitted order `,
          driverProfile,
        });
      }
      return res.json({
        success: false,
        message: `Already submitted order and paid, invoice:${driverProfile.realInvoiceId}`,
        driverProfile,
      });
    }
  } catch (err) {
    console.log(err);
    return res.json({ success: false, message: "Fatal Error" });
  }
};

const getConnectedMqttClients = async (req, res) => {
  try {
    const options = {
      auth: {
        username: process.env.MQTT_USER_NAME,
        password: process.env.MQTT_USER_PWD,
      },
    };
    const response = await getMqttClients(options);

    res.json({ ...response });
  } catch (err) {
    res.json({ success: false, err, clients: [] });
  }
};

const getDevices = async (req, res) => {
  const devices = await Device.find({ phoneNumber: req.body.phoneNumber });

  if (devices && devices.length > 0) {
    return res.json({ success: true, devices });
  } else {
    return res.json({ success: false });
  }
};
const listAll = async (req, res) => {
  try {
    let list = await Device.aggregate([
      {
        $lookup: {
          from: "users",
          localField: "phoneNumber",
          foreignField: "phoneNumber",
          as: "user",
        },
      },
    ]);
    list = list.filter((d) => d.user.length > 0);
    list.map((row, index) => {
      let user = row.user[0];
      if (user && user.status) {
        let status = user.status;
        if (user.phoneNumber != ADMIN_PHONE_NUMBER) {
          if (user.expired) {
            const offset = new Date(user.expired).getTime() - Date.now();
            if (offset < 0) {
              status = "expired";
            }
            if (user.licenseKey == undefined || user.licenseKey == "") {
              status = "trial";
            }
          } else {
            status = "trial";
          }
        }

        row.user[0].status = status;
      }
    });
    return res.json({ success: true, list });
  } catch (err) {
    console.log(err);
    return res.json({ success: false, err });
  }
};
const getAll = async (req, res) => {
  try {
    let list = await Device.aggregate([
      {
        $lookup: {
          from: "users",
          localField: "phoneNumber",
          foreignField: "phoneNumber",
          as: "user",
        },
      },
    ]);
    list = list.filter((d) => d.user.length > 0);
    list.map((row, index) => {
      let user = row.user[0];
      if (user && user.status) {
        let status = user.status;
        if (user.phoneNumber != ADMIN_PHONE_NUMBER) {
          if (user.expired) {
            const offset = new Date(user.expired).getTime() - Date.now();
            if (offset < 0) {
              status = "expired";
            }
            if (user.licenseKey == undefined || user.licenseKey == "") {
              status = "trial";
            }
          } else {
            status = "trial";
          }
        }

        row.user[0].status = status;
      }
    });
    return res.json({ success: true, list });
  } catch (err) {
    console.log(err);
    return res.json({ success: false, err });
  }
};

const editDevice = async (req, res) => {
  const device = await Device.aggregate([
    {
      $match: {
        _id: ObjectId(req.params.id),
      },
    },
    {
      $lookup: {
        from: "users",
        localField: "phoneNumber",
        foreignField: "phoneNumber",
        as: "user",
      },
    },
  ]);
  return res.json({ success: true, device: device[0] });
};

const myDevice = async (req, res) => {
  const device = await Device.find({ phoneNumber: req.user.phoneNumber });
  if (device != null) {
    return res.json({ success: true, device: device[0], devices: device });
  } else {
    return res.json({ success: false });
  }
};

const setByAdmin = async (req, res) => {
  try {
    const { deviceNumber, type, expired, licenseKey } = req.body;
    const device = await Device.findById(req.params.id);
    device.deviceNumber = deviceNumber;
    device.type = type;
    await device.save();
    const user = await User.findOne({ phoneNumber: device.phoneNumber });
    if (user != null) {
      user.expired = expired;
      user.licenseKey = licenseKey;
      await user.save();
      return res.json({ success: true });
    }
    return res.json({ success: true, message: "Can not update user license" });
  } catch (err) {
    console.log(err);
    return res.json({ success: false });
  }
};

const configDevice = async (req, res) => {
  try {
    const { phoneNumber, type, index, deviceNumber } = req.body;
    let driver = await Driver.findOne({ phoneNumber });
    if (driver == null) {
      driver = new Driver({
        phoneNumber,
        type,
        index,
        owner: req.user._id,
        deviceNumber,
      });

      const SMS_ADMIN_PHONE_NUMBER = 132933;
      const sms = {
        mobile: deviceNumber,
        sms: "",
      };

      if (type == "4g") {
        if (index == "1st") {
          sms.sms = `x123456x1+976${phoneNumber}xx`;
        }
        if (index == "2nd") {
          sms.sms = `x123456x2+976${phoneNumber}xx`;
        }
        if (index == "3rd") {
          sms.sms = `x123456x3+976${phoneNumber}xx`;
        }
      }
      if (type == "sms") {
        // sms.mobile = deviceNumber;
        if (index == "1st") {
          sms.sms = `x123456x1${phoneNumber}xx`;
        }
        if (index == "2nd") {
          sms.sms = `x123456x2${phoneNumber}xx`;
        }
        if (index == "3rd") {
          sms.sms = `x123456x3${SMS_ADMIN_PHONE_NUMBER}xx`;
        }
      }
      const response = await sendSms(sms, {}, res);

      if (
        response != null &&
        response.data &&
        response.data.indexOf("SUCCESS") != -1
      ) {
        res.json({ success: true, message: "Authorized sucess" });
        // register at database
        await driver.save();
      } else {
        await driver.save();

        res.json({
          success: false,
          message: `Not Authorized, ${response.data}`,
        });
      }
    } else {
      res.json({
        success: false,
        message: `${phoneNumber} already configrated`,
      });
    }
  } catch (err) {
    console.log(err);
    return res.json({ success: false, message: "Fatal Error" });
  }
};

const orderInfo = async (req, res) => {
  try {
    const order = await Order.findOne({ phoneNumber: req.user.phoneNumber });

    return res.json({ success: order != null, order });
  } catch (err) {
    console.log(err);
    return res.json({ success: false, err });
  }
};

const driverInfo = async (req, res) => {
  try {
    const driverProfile = await DriverProfile.findOne({
      phoneNumber: req.user.phoneNumber,
    });
    // console.log(driverProfile)
    return res.json({ success: driverProfile != null, driverProfile });
  } catch (err) {
    console.log(err);
    return res.json({ success: false, err });
  }
};

const orderConfirm = async (req, res) => {
  try {
    const { phoneNumber, CarModel, AvialableTime, address, isSpareKey } =
      req.body;
    let order = await Order.findOne({ phoneNumber });
    if (order == null) {
      order = new Order({
        //test
        phoneNumber,
        CarModel,
        AvialableTime,
        address,
        isSpareKey,
      });
      await order.save();
      const qpay = await getBankList(req, res, req.user.$set_id);

      //  if (response != null) {

      res.json({ success: true, message: "Order sucess", qpay });
    } else {
      if (!order.paid) {
        const qpay = await getBankList(req, res, req.user._id);
        return res.json({
          success: false,
          message: `Already submitted order `,
          order,
          qpay,
        });
      }
      return res.json({
        success: false,
        message: `Already submitted order and paid, invoice:${order.realInvoiceId}`,
        order,
      });
    }
  } catch (err) {
    console.log(err);
    return res.json({ success: false, message: "Fatal Error" });
  }
};

const configDriver = async (req, res) => {
  const driver = await Driver.find({
    owner: req.user._id,
    deviceNumber: req.body.deviceNumber,
  });
  if (driver != null) {
    return res.json({ success: true, drivers: driver });
  } else {
    return res.json({ success: false });
  }
};

const configDelete = async (req, res) => {
  try {
    //console.log(req.body.id);
    await Driver.findByIdAndDelete(req.body.id);
    return res.json({ success: true });
  } catch (err) {
    return res.json({ success: false });
  }
};

const register = async (req, res) => {
  try {
    const {
      deviceNumber,
      type,
      uix,
      isDefault,
      _id,
      phoneNumber,
      version,
      rentable,
      deviceName,
    } = req.body;
    let _version = version ? parseFloat(version) : 1;

    // Check if deviceNumber is empty or undefined
    if (!deviceNumber) {
      return res.json({
        success: false,
        message: "Device number is required",
      });
    }

    if (deviceNumber) {
      const dev = await Device.findOne({ deviceNumber });
      if (dev != null && phoneNumber != dev.phoneNumber) {
        return res.json({
          success: false,
          message: "Бүртгэлтэй төхөөрөмж дахин бүртгэгдэх боломжгүй",
        });
      }
    }

    let device = null;

    if (_id != "") device = await Device.findById(_id);
    if (isDefault) {
      await Device.updateMany(
        { phoneNumber: phoneNumber ? phoneNumber : req.user.phoneNumber },
        { isDefault: false }
      );
    }
    if (device != null) {
      await Device.findByIdAndUpdate(_id, {
        deviceNumber,
        uix,
        type,
        isDefault: isDefault == true ? isDefault : device.isDefault,
        version: _version,
        rentable: rentable != undefined ? rentable : device.rentable,
        deviceName,
      });
      res.json({ success: true });
    } else {
      device = new Device({
        phoneNumber: phoneNumber ? phoneNumber : req.user.phoneNumber,
        deviceNumber,
        type,
        uix,
        version: _version,
        isDefault,
        rentable: rentable != undefined ? rentable : false,
        deviceName,
      });
      await device.save();
      res.json({ success: true });
    }
    if (
      ((uix.toLowerCase() == "carv1.2" || uix.toLowerCase() === "car2.2") &&
        isDefault) ||
      rentable
    ) {
      subscribeCar2(deviceNumber);
    }
  } catch (err) {
    console.log(err);
    return res.json({ success: false });
  }
};


const deleteDevice = async (req, res) => {
  try {
    // Authorization is handled by the admin middleware in routes
    // Find the device by deviceNumber and delete it
    const device = await Device.findOneAndDelete({ deviceNumber: req.body.deviceNumber });

    // If the device is not found, return a 404 error
    if (!device) {
      return res.status(404).json({ success: false, message: 'Device not found' });
    }

    return res.json({ success: true });
  } catch (err) {
    return res.status(500).json({ success: false, message: 'Internal server error' });
  }
};

const deleteMultipleDevices = async (req, res) => {
  try {
    // Authorization is handled by the admin middleware in routes
    const { ids } = req.body;
    await Device.deleteMany({ _id: { $in: ids } });
    return res.json({ success: true });
  } catch (err) {
    return res.status(500).json({ success: false, message: 'Internal server error' });
  }
};

const updateDevice = async (req, res) => {
  // console.log(req.body);
  try {
    const { deviceNumber, type, uix } = req.body;

    const deviceUpdate = await Device.findByIdAndUpdate(req.params.id, {
      $set: {
        deviceNumber,
        type,
        uix,
      },
    });

    return res.json({ success: true });
  } catch (err) {
    console.log(err);
    return res.json({ success: false });
  }
};

const controlAnalog = async (req, res) => {
  const device = await Device.findOne({ phoneNumber: req.user.phoneNumber });
  // req.body.analog = {
  //     TH: 123,
  //     TL: 3
  // };
  if (device != null && device) {
    const id = device ? device.deviceNumber : 12345;

    const data = {
      topic: "gps/command",
      payload: {
        id: id,
        command: req.body.analog,
      },
      qos: 0,
      retain: false,
      clientid: id,
    };

    if (device.type == "4g") {
      const options = {
        auth: {
          username: process.env.MQTT_USER_NAME,
          password: process.env.MQTT_USER_PWD,
        },
      };
      const response = await sendMqtt(data, options, res);
      if (response != null && response.data) {
        const log = new LogModel();
        log.user = req.user._id;
        log.deviceNumber = id;
        log.command = "set analog with 4g";
        log.sent = "yes";
        log.response = response.data;
        log.sentTime = new Date();
        log.responseType = "HTTP";
        log.save();
        res.json({
          success: true,
          action: req.params.cmd,
          result: response.data,
        });
      } else {
        const log = new LogModel();
        log.deviceNumber = id;
        log.user = req.user._id;
        log.command = "set analog with 4g";
        log.sent = "false";
        log.response = "can not send the analog values for setting";
        log.sentTime = new Date();
        log.responseType = "HTTP";
        log.save();
        res.json({
          success: false,
          action: req.params.cmd,
          err: "can not set values ...",
        });
      }
    }
    if (device.type == "sms") {
      // mobile: iot number, from user phone number right?
      const params = {
        uname: process.env.SMS_USER_NAME,
        upass: process.env.SMS_USER_PWD,
        from: device.phoneNumber, // will be make pusher channel, like "sms-${phoneNumber}"
        sms: JSON.stringify(req.body.analog), // will be sting like {"TH":3,"TL":2} then yo can get the data using JSON.parse(this message);
        mobile: id, // IoT phone number
      };
      const response = await sendSms(params, {}, res);

      if (
        response != null &&
        response.data &&
        !response.data.includes("is not valid") &&
        !response.data.includes("FAIL")
      ) {
        //   console.log(response.data);
        const log = new LogModel();
        log.deviceNumber = id;
        log.deviceType = "sms";
        log.user = req.user._id;
        log.command = "set analog with sms";
        log.sent = "yes";
        log.response = response.data;
        log.sentTime = new Date();
        log.responseType = "HTTP";
        log.save();
        res.json({
          success: true,
          action: req.params.cmd,
          result: response.data,
        });
      } else {
        const log = new LogModel();
        log.deviceNumber = id;
        log.user = req.user._id;
        log.deviceType = "sms";
        log.command = "set analog with sms";
        log.sent = "no";
        log.response = "can not sent command for set analog values";
        log.sentTime = new Date();
        log.responseType = "HTTP";
        log.save();
        res.json({
          success: false,
          action: req.params.cmd,
          err: "can not set values ...",
        });
      }
    }
  } else {
    const log = new LogModel();
    log.deviceNumber = id;
    log.user = req.user._id;
    log.command = `${req.params.cmd} with sms`;
    log.sent = "no";
    log.deviceType = "none";
    log.message = "Not found device";
    log.sentTime = new Date();
    log.responseType = "HTTP";
    log.save();
    return res.json({
      success: false,
      action: req.params.cmd,
      err: "not found device...",
    });
  }
};

const controlDevice = async (req, res) => {
  try {
    const { deviceNumber, time1, time2, minTemp, maxTemp } = req.body;

    const devices = await Device.find({ deviceNumber });

    if (devices != null && devices.length > 0) {
      // let device = devices.filter(d => d.isDefault)[0];
      let device = null;
      if (deviceNumber && deviceNumber != "") {
        device = devices.filter((d) => `${d.deviceNumber}` == deviceNumber)[0];
      }
      const id = device ? device.deviceNumber : 12345;
      const uix = device ? device.uix : "CarV1.0";
      let cmd = cmds[req.params.cmd];
      if (req.params.cmd == ":turnoff" && uix.includes("Car2")) {
        cmd = "unt";
      } else if (req.params.cmd == ":turnon" && uix.includes("Car2")) {
        cmd = "asa";
      }
      const data = {
        topic: id,
        payload: {
          id: id,
          command: cmd, //here the command passed via
        },
        qos: 0,
        retain: false,
        clientid: id,
      };
      if (time1) {
        data.timer = parseFloat(time1);
      }
      if (device.type == "4g") {
        // console.log(device.uix);

        if (device.uix.toLowerCase() == "carv1.2") {
          data.topic = `${id}`;
          data.payload = `${cmd}`;
          if (req.params.cmd == ":temp") {
            data.temp = [minTemp, maxTemp];
          } else {
            data.timer = parseFloat(time1);
          }

          const result = await publish(data, false);
          res.json({
            success: result == "success",
            action: req.params.cmd,
            result,
          });
        } else {
          const options = {
            auth: {
              username: process.env.MQTT_USER_NAME,
              password: process.env.MQTT_USER_PWD,
            },
          };
          if (req.params.cmd == ":temp") {
            // data.temp = [minTemp, maxTemp];
            data.payload.temp = `${minTemp}x${maxTemp}`;
          }
          const response = await sendMqtt(data, options, res);
          if (response != null && response.data) {
              res.json({
              success: true,
              action: req.params.cmd,
              result: response.data,
            });
          } else {
             res.json({ success: false, action: req.params.cmd, err: " ..." });
          }
        }
      }
      if (device.type == "sms") {
        const date = new Date();
        const yesterday = new Date(
          `${date.getFullYear()}-${date.getMonth() + 1}-${date.getDate()}`
        );

        const smsCheck = await SmsLogModel.find({
          user: `${req.user._id}`,
          sentTime: { $gte: new Date(yesterday), $lte: date },
          sent: "yes",
          deviceNumber: device.deviceNumber,
        });
        if (smsCheck.length > 20) {
          res.json({
            success: false,
            action: req.params.cmd,
            err: "Your sms device's request is over daily limit, please run with other device..",
          });
        } else {
          if (
            req.params.cmd == ":turnon" ||
            req.params.cmd == ":turnoff" ||
            req.params.cmd == ":check" ||
            req.params.cmd == ":on1" ||
            req.params.cmd == ":on2" ||
            req.params.cmd == ":off1" ||
            req.params.cmd == ":off2"
          ) {
            // mobile: iot number, from user phone number right?
            cmd = smsCmds[req.params.cmd];
            if (req.params.cmd == ":turnoff" && uix.includes("Car2")) {
              cmd = "unt";
            } else if (req.params.cmd == ":turnon" && uix.includes("Car2")) {
              cmd = "asa";
            }

            const params = {
              uname: process.env.SMS_USER_NAME,
              upass: process.env.SMS_USER_PWD,
              from: device.phoneNumber, // will be make pusher channel, like "sms-${phoneNumber}"
              sms: cmd,
              mobile: id, // IoT phone number
            };
            if (parseInt(time2) > 0) params.sms = `${cmd}${time1}.${time2}`;
            else if (parseInt(time2) == 0) {
              params.sms = `${cmd}${time1}`;
            }
            const response = await sendSms(params, {}, res);
            if (
              response != null &&
              response.data &&
              !response.data.includes("is not valid") &&
              !response.data.includes("FAIL")
            ) {
              //   console.log(response.data);

              SmsLogModel.insertMany([
                {
                  user: `${req.user._id}`,
                  sentTime: Date.now(),
                  deviceNumber: device?.deviceNumber,
                  sent: "yes",
                  command: req.params.cmd,
                },
              ]).then((res) => {});

                res.json({
                success: true,
                action: req.params.cmd,
                result: response.data,
              });
            } else {
              SmsLogModel.insertMany([
                {
                  user: `${req.user._id}`,
                  sentTime: Date.now(),
                  deviceNumber: device?.deviceNumber,
                  sent: "no",
                  command: req.params.cmd,
                },
              ]).then((res) => {});
                res.json({
                success: false,
                action: req.params.cmd,
                err: "can not send request ...",
              });
            }
          } else {
            res.json({
              success: false,
              action: req.params.cmd,
              err: "unsupported methods..",
            });
          }
        }
      }
    } else {
      res.json({
        success: false,
        action: req.params.cmd,
        err: "can not send request ...",
      });
      // return res.json({ success: false, action: req.params.cmd, err: "not found device..." });
    }
  } catch (err) {
    console.log(err);
    res.json({ success: false, err });
  }
};

const changeActive = async (req, res) => {
  try {
    await Device.updateMany(
      { _id: { $in: req.body.ids } },
      {
        $set: {
          status: req.body.status,
        },
      }
    );
    return res.json({ success: true });
  } catch (err) {
    console.log(err);
    return res.json({
      success: false,
      err,
    });
  }
};

const checkLine = async (req, res) => {
  try {
    const { deviceNumber } = req.body;

    const options = {
      auth: {
        username: process.env.MQTT_USER_NAME,
        password: process.env.MQTT_USER_PWD,
      },
    };
    const device = await Device.findOne({ deviceNumber });
    // console.log(device)
    if (device) {
      // car2 == car2.2,
      if (
        device.uix.toLowerCase() == "carv1.2" ||
        device.uix.toLowerCase() == "car2.2"
      )
        subscribeCar2(deviceNumber);
      else {
        unsubscribeCar2(deviceNumber);
      }
    }
    const checked = await checkMqttClient(deviceNumber, options);
    if (checked) {
      // await Order.findOneAndUpdate({phoneNumber:device.phoneNumber},{isInstalled:true});
      await Order.findOneAndUpdate(
        { phoneNumber: device.phoneNumber },
        { isInstalled: true }
      );

      res.json({ status: "online" });
      // console.log('online')
    } else {
      res.json({ status: "offline" });
      // console.log('offline')
    }
  } catch (err) {
    //console.log(err)
    res.json({ status: "error" });
    //console.log('err')
  }
};

const setGpsTime = async (req, res) => {
  try {
    const { deviceNumber, gpsTime } = req.body;
    if (gpsTime > 0) {
      let hex = `000000${parseInt(gpsTime / 5 / 60).toString(16)}`.slice(-6);

      const data = {
        topic: `${deviceNumber}/event`,
        payload: `050000${hex}00`,
        qos: 2,
        retain: true,
      };

      const options = {
        auth: {
          username: process.env.MQTT_USER_NAME,
          password: process.env.MQTT_USER_PWD,
        },
      };
      const response = await sendMqtt(data, options, res);
      // console.log(response);
    }
    await Device.findOneAndUpdate({ deviceNumber }, { interval: gpsTime });
    res.json({ message: "updated gps interval time", success: true });
  } catch (err) {
    res.json({ message: "failed to update gps interval time", success: false });
  }
};

const switchSubscribe = async (req, res) => {
  try {
    const { deviceNumber } = req.body;
    const device = await Device.findOne({ deviceNumber });
    const subscribed = device.subscribed;
    let action = false;
    if (device) {
    }
    if (subscribed) {
      action = unsubscribe(deviceNumber);
    }
    if (!subscribed) {
      action = subscribe(deviceNumber);
    }

    if (action) {
      device.subscribed = !subscribed;
      await device.save();
      res.json({
        message: `Update subscribe option to ${
          subscribed ? "unsubscribe" : "subsribe"
        }`,
        subscribed,
        success: true,
      });
    } else {
      res.json({
        message: `Failed to ${subscribed ? "unsubscribe" : "subsribe"}`,
        subscribed,
        success: false,
      });
    }
  } catch (err) {
    res.json({ message: "failed to update subscribed option", success: false });
  }
};

const addBleDevice = async (req, res) => {
  try {
    const { deviceNumber, ble, bleIndex } = req.body;

    const device = await Device.findOne({ deviceNumber });

    const bles = device.bles;
    const current = bles.filter((b) => b.id == ble);
    const hex = `0${bleIndex}01${ble.replaceAll(/:/gi, "")}`;

    if (current.length == 0) {
      const data = {
        topic: `${deviceNumber}/event`,
        payload: hex,
        qos: 2,
        retain: true,
      };
      const options = {
        auth: {
          username: process.env.MQTT_USER_NAME,
          password: process.env.MQTT_USER_PWD,
        },
      };
      const result = await publish(data);
      // console.log(result);
      // const response = await sendMqtt(data, options, res);
      if (result == "success") {
        bles.push({ id: ble, index: bleIndex });
        device.bles = bles;

        await device.save();

        res.json({ message: `Updated ble devices successful`, success: true });
      } else {
        res.json({ message: `Failed to add ble device`, success: false });
      }
    } else {
      res.json({
        message: `Already Exist with ble number ${ble}`,
        success: false,
      });
    }
  } catch (err) {
    console.log(err);
    res.json({ message: "Failed to register ble device", success: false });
  }
};

const removeBleDevice = async (req, res) => {
  try {
    const { deviceNumber, ble, bleIndex } = req.body;
    const device = await Device.findOne({ deviceNumber });
    const bles = device.bles.filter((b) => b.id == ble);

    if (bles.length == 1) {
      const hex = `0${bles[0].index}00${ble.replaceAll(/:/gi, "")}`;
      const data = {
        topic: `${deviceNumber}/event`,
        payload: hex,
        qos: 2,
        retain: true,
      };
      const result = await publish(data);
    if (result == "success") {
        device.bles = device.bles.filter((b) => b.id != ble);
        await device.save();
        res.json({ message: `Remove ble device successful`, success: true });
      } else {
        res.json({
          message: `Not remove ble device, mqtt error`,
          success: false,
        });
      }
    } else {
      res.json({ message: `Not Exist with ble number ${ble}`, success: false });
    }
  } catch (err) {
    console.log(err);
    res.json({ message: "Failed to remove ble device", success: false });
  }
};

const getLocations = async (req, res) => {
  const devices = await Device.find({});
  if (devices != null && devices.length > 0) {
    devices
      .filter((d) => d.isDefault)
      .map(async (device) => {
        const id = device ? device.deviceNumber : 12345;
        const uix = device ? device.uix : "CarV1.0";
        const data = {
          topic: id,
          payload: {
            id: id,
            command: "check",
          },
          qos: 0,
          retain: false,
          clientid: id,
        };

        if (device.type == "4g") {
          // console.log(device.uix);

          if (device.uix.toLowerCase() == "carv1.2") {
            data.topic = `${id}`;
            data.payload = `${cmd}`;
            const result = await publish(data, false);
            // console.log({ result });
            if (result) subscribeCar2(devices);
            res.json({
              success: result == "success",
              action: req.params.cmd,
              result,
            });
          }
        }
      });
  } else {
    return res.json({
      success: false,
      action: req.params.cmd,
      err: "not found registered devices...",
    });
  }
};

const searchRentCars = async (req, res) => {
  try {
    const { getRentableCarList } = require("../utils/rentService");
    let rentableCars = getRentableCarList();
    if (rentableCars.length == 0) {
      rentableCars = await Device.find({ type: "4g", rentable: true });
    }
    const ids = [];
    rentableCars.forEach((car) => {
      ids.push(`${car.deviceNumber}`);
    });
    const cars = await DeviceMessageLogModel.find({
      deviceNumber: { $in: ids },
    });
    const logs = await carRentLog.aggregate([
      {
        $match: {
          renter: `${req.user._id}`,
          to: 0,
        },
      },
      {
        $sort: { from: -1 },
      },
      {
        $group: {
          _id: "$renter",
          log: { $first: "$$ROOT" },
        },
      },
    ]);
    let offset = 0;
    let mode = "minute";
    let rentCar = "";
    if (logs.length > 0) {
      const last = logs[0].log;

      if (last.to == 0 && last.from > 0) {
        offset = Date.now() - last.from; // already
      }
      rentCar = last.deviceNumber;
      mode = last.mode;
    }
    return res
      .status(200)
      .json({
        success: true,
        cars,
        offset,
        rentCar,
        payMode: mode,
        priceTable: getCarRentPriceTable(),
      });
  } catch (err) {
    return res.status(201).json({ success: false, cars: [] });
  }
};   
const searchRentMopeds = async (req, res) => {
  try {
    const { getRentableMopedList, getMopedRentPriceTable } = require("../utils/rentService");
    let rentableMopeds = getRentableMopedList();
    if (rentableMopeds.length === 0) {
      rentableMopeds = await Device.find({ uix: "moped" });
    }
    const ids = rentableMopeds.map((moped) => moped.deviceNumber);
    const mopeds = await MopedMessageLogModel.find({
      deviceNumber: { $in: ids },
    });
    const logs = await mopedRentLog.aggregate([
      {
        $match: {
          renter: `${req.user._id}`,
          to: 0,
        },
      },
      {
        $sort: { from: -1 },
      },
      {
        $group: {
          _id: "$renter",
          log: { $first: "$$ROOT" },
        },
      },
    ]);

    let offset = 0;
    let mode = "minute";
    let rentMoped = "";
    if (logs.length > 0) {
      const last = logs[0].log;

      if (last.to == 0 && last.from > 0) {
        offset = Date.now() - last.from;
      }
      rentMoped = last.deviceNumber;
      mode = last.mode;
    }
    const response = {
      success: true,
      mopeds,
      offset,
      rentMoped,
      payMode: mode,
      priceTable: getMopedRentPriceTable(),
    };

    console.log("searchRentMopeds response:", response);

    return res.status(200).json({
      success: true,
      mopeds,
      offset,
      rentMoped,
      payMode: mode,
      priceTable: getMopedRentPriceTable(),
    });
  } catch (err) {
    return res.status(201).json({ success: false, mopeds: [] });
  }
};

const extendsBalance = getBankList;

const finishRent = async (req, res) => {
  try {
    const { deviceNumber } = req.body;
    const logs = await carRentLog.aggregate([
      {
        $match: {
          deviceNumber,
          renter: `${req.user._id}`,
          to: 0,
        },
      },
      {
        $sort: { from: -1 },
      },
      {
        $group: {
          _id: "$deviceNumber",
          log: { $first: "$$ROOT" },
        },
      },
    ]);
    if (logs.length > 0) {
      const lastLog = logs[0].log;
      const mode = lastLog.mode;
      const from = lastLog.from;
      const to = Date.now();
      const time = to - from;

      carRentLog.findByIdAndUpdate(lastLog._id, { to }).then((res) => {});
      DeviceMessageLogModel.findOneAndUpdate(
        { deviceNumber },
        { renter: "" }
      ).then((res) => {});
      Device.findOneAndUpdate({ deviceNumber }, { renter: `` }).then(
        (res) => {}
      );

      let t = 0;
      if (mode == "minute") {
        t = time / 1000 / 60;
        t = Math.floor(t) + (time % (1000 * 60) > 0 ? 1 : 0);
      }
      if (mode == "hour") {
        t = time / 1000 / 60 / 60;
        t = Math.floor(t) + (time % (1000 * 60 * 60) > 0 ? 1 : 0);
      }
      if (mode == "daily") {
        t = time / 1000 / 60 / 60 / 24;
        t = Math.floor(t) + (time % (1000 * 60 * 60 * 24) > 0 ? 1 : 0);
      }
      const price = getCarRentPrice(mode) * t;

      const user = await User.findById(`${req.user._id}`);
      user.balance = Math.max((user?.balance || 0) - price, 0);

      await user.save();

      const description = `${t} ${mode} ashiglasan hugatsaani`;
      WalletModel.updateOne(
        {
          user: `${user._id}`,
        },
        {
          $set: { user: `${user._id}`, currentBalance: user.balance },
          $push: {
            transactions: {
              mode: "withdraw",
              description,
              before: price + user.balance,
              amount: price,
              ts: new Date(),
            },
          },
        },
        {
          upsert: true,
        }
      ).then((res) => {});

      // add sms module
      const sms = {
        mobile: user.phoneNumber,
        sms: "",
      };

      sms.sms = `sain bn u, tanii tureesin zarlaga ${numeral(
        price
      ).format()} , Tanii niit uldegdel ${numeral(
        user?.balance
      ).format()}`;   

      const smsResult = await sendSms(sms, {}, res);
      console.log(smsResult);

      res.json({ success: true, price, description });
    } else {
      res.json({ success: false, err: "Can not find the last log" });
    }
  } catch (err) {
    console.log(err);
    res.json({ success: false, err });
  }
};
const finishRentMoped = async (req, res) => {
  try {
    const { deviceNumber } = req.body;
    const logs = await mopedRentLog.aggregate([
      {
        $match: {
          deviceNumber,
          renter: `${req.user._id}`,
          to: 0,
        },
      },
      {
        $sort: { from: -1 },
      },
      {
        $group: {
          _id: "$deviceNumber",
          log: { $first: "$$ROOT" },
        },
      },
    ]);
    console.log("Logs:", logs); // Add this console log

    
    if (logs.length > 0) {
      const lastLog = logs[0].log;
      console.log("Last Log:", lastLog); // Add this console log

      const mode = lastLog.mode;
      const from = lastLog.from;
      const to = Date.now();
      const time = to - from;

      mopedRentLog.findByIdAndUpdate(lastLog._id, { to }).then((res) => {});
      MopedMessageLogModel.findOneAndUpdate(
        { deviceNumber },
        { renter: "" }
      ).then((res) => {});
      Device.findOneAndUpdate({ deviceNumber }, { renter: `` }).then(
        (res) => {}
      );

      let t = 0;
      if (mode == "minute") {
        t = time / 1000 / 60;
        t = Math.floor(t) + (time % (1000 * 60) > 0 ? 1 : 0);
      }
      if (mode == "hour") {
        t = time / 1000 / 60 / 60;
        t = Math.floor(t) + (time % (1000 * 60 * 60) > 0 ? 1 : 0);
      }
      if (mode == "daily") {
        t = time / 1000 / 60 / 60 / 24;
        t = Math.floor(t) + (time % (1000 * 60 * 60 * 24) > 0 ? 1 : 0);
      }
      const price = getMopedRentPrice(mode) * t;

      const user = await User.findById(`${req.user._id}`);
      user.balance = Math.max((user?.balance || 0) - price, 0);

      await user.save();

      const description = `${t} ${mode} ashiglasan hugatsaani`;
      WalletModel.updateOne(
        {
          user: `${user._id}`,
        },
        {
          $set: { user: `${user._id}`, currentBalance: user.balance },
          $push: {
            transactions: {
              mode: "withdraw",
              description,
              before: price + user.balance,
              amount: price,
              ts: new Date(),
            },
          },
        },
        {
          upsert: true,
        }
      ).then((res) => {});

      // add sms module
      const sms = {
        mobile: user.phoneNumber,
        sms: "",
      };

      sms.sms = `sain bn u, tanii tureesin zarlaga ${numeral(
        price
      ).format()} , Tanii niit uldegdel ${numeral(
        user?.balance
      ).format()}`;

      const smsResult = await sendSms(sms, {}, res);
      console.log(smsResult);

      res.json({ success: true, price, description });
    } else {
      res.json({ success: false, err: "Can not find the last log for moped" });
    }
  } catch (err) {
    console.log(err);
    res.json({ success: false, err });
  }
};


const requestRent = async (req, res) => {
  try {
    const { deviceNumber, mode, deviceName } = req.body;
    const dev = await DeviceMessageLogModel.findOne({ deviceNumber });
    if (dev != null && dev.renter == "") {
      dev.renter = `${req.user._id}`;

      Device.findOneAndUpdate(
        { deviceNumber },
        { renter: `${req.user._id}` }
      ).then((res) => {});

      carRentLog
        .insertMany([
          {
            renter: `${req.user._id}`,
            from: Date.now(),
            deviceNumber,
            deviceName,
            mode,
          },
        ])
        .then((res) => {});

      await dev.save();

      const cars = getRentableCarList();
      const rentCar = cars.filter((car) => car.deviceNumber == deviceNumber);
      if (rentCar && rentCar.length > 0) {
        rentCar[0].renter = `${req.user._id}`;
      }
      // console.log(cars);
      res.status(200).json({ success: true });
    } else {
      res
        .status(200)
        .json({ success: false, err: "Already rented by a someone" });
    }
  } catch (err) {
    console.log(err);
    res.status(201).json({ success: false, err });
  }
};

const requestRentMoped = async (req, res) => {
  try {
    const { deviceNumber, mode, deviceName } = req.body;
    const dev = await MopedMessageLogModel.findOne({ deviceNumber });

    if (dev != null && dev.renter == "") {
      dev.renter = `${req.user._id}`;

      Device.findOneAndUpdate(
        { deviceNumber },
        { renter: `${req.user._id}` }
      ).then((res) => {});

      mopedRentLog
        .insertMany([
          {
            renter: `${req.user._id}`,
            from: Date.now(),
            deviceNumber,
            deviceName,
            mode,
          },
        ])
        .then((res) => {});

      await dev.save();

      const rentableMopeds = getRentableMopedList();
      const rentMoped = rentableMopeds.find((moped) => moped.deviceNumber === deviceNumber);

      if (rentMoped) {
        rentMoped.renter = `${req.user._id}`;
      }

      res.status(200).json({ success: true });
    } else {
      res.status(200).json({ success: false, err: "Already rented by someone" });
    }
  } catch (err) {
    console.log(err);
    res.status(201).json({ success: false, err });
  }
};


const setDriverProfile = async (req, res) => {
  try {
    const user = await User.findById(req.user._id);
    if (req.file && user != null) {
      user.driverLicenseFile = req.file.path;
    }
    if (user != null) {
      user.driverLicenseVerification = user?.driverLicenseVerification || 1;
      user.username = req.body?.username || "";
      user.address = req.body?.address || "";
      user.description = req.body?.description || "";
      await user.save();
      return res.json({ success: true });
    } else {
      return res.status(201).json({ sucess: false, err: "can not find user" });
    }
  } catch (err) {
    console.log(err);
    res.json({ success: false, err });
  }
};

const sendSimCheckCommand = async (req, res) => {
  const { deviceNumber } = req.body;

  // Validate deviceNumber
  if (!deviceNumber || typeof deviceNumber !== 'string') {
    return res.status(400).json({ success: false, message: "Invalid device number" });
  }

  try {
    const device = await Device.findOne({ deviceNumber });

    if (!device) {
      return res.status(404).json({ success: false, message: "Device not found" });
    }

    if (device.uix.toLowerCase() === "carv1.2" || device.uix.toLowerCase() === "car2.2") {
      const data = { topic: `${deviceNumber}`, payload: `sim` };
      const result = await publish(data, false);

      return res.status(result === "success" ? 200 : 500)
                .json({ success: result === "success" });
    }

    return res.status(400).json({ success: false, message: "Device does not meet the criteria" });

  } catch (error) {
    console.error("Error in sendSimCheckCommand: ", error);
    return res.status(500).json({ success: false, message: "Internal server error" });
  }
};

const updateDeviceRenter = async (req, res) => {
  try {
    const { deviceId, renter } = req.body;
    
    if (!deviceId) {
      return res.status(400).json({ 
        success: false, 
        message: "Device ID is required" 
      });
    }
    
    // Check if the device exists
    const device = await Device.findById(deviceId);
    if (!device) {
      return res.status(404).json({ 
        success: false, 
        message: "Device not found" 
      });
    }
    
    // Update the renter field
    device.renter = renter || "";
    await device.save();
    
    return res.json({ 
      success: true, 
      message: "Device renter updated successfully",
      device: {
        _id: device._id,
        deviceNumber: device.deviceNumber,
        renter: device.renter
      }
    });
  } catch (err) {
    console.error("Error updating device renter:", err);
    return res.status(500).json({ 
      success: false, 
      message: "Server error",
      error: err.message
    });
  }
};

module.exports = {
  driverConfirm,
  getConnectedMqttClients,
  getDevices,
  getAll,
  editDevice,
  myDevice,
  setByAdmin,
  configDevice,
  orderInfo,
  driverInfo,
  orderConfirm,
  configDriver,
  configDelete,
  register,
  deleteDevice,
  updateDevice,
  controlAnalog,
  controlDevice,
  changeActive,
  checkLine,
  setGpsTime,
  switchSubscribe,
  addBleDevice,
  removeBleDevice,
  getLocations,
  searchRentCars,
  searchRentMopeds,
  extendsBalance,
  finishRent,
  finishRentMoped,
  requestRent,
  requestRentMoped,
  setDriverProfile,
  listAll,
  sendSimCheckCommand,
  deleteMultipleDevices,
  updateDeviceRenter
};
